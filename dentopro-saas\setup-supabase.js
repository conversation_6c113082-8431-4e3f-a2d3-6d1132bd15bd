// Supabase Setup Script for DentoPro
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials in .env.local');
  process.exit(1);
}

// Create Supabase admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupDatabase() {
  console.log('🚀 Starting DentoPro Database Setup...\n');

  try {
    // Step 1: Create initial schema
    console.log('📋 Step 1: Creating database schema...');
    const schemaSQL = fs.readFileSync(
      path.join(__dirname, 'supabase/migrations/20241221000001_initial_schema.sql'),
      'utf8'
    );
    
    const { error: schemaError } = await supabase.rpc('exec_sql', { sql: schemaSQL });
    if (schemaError) {
      console.error('❌ Schema creation failed:', schemaError);
    } else {
      console.log('✅ Database schema created successfully');
    }

    // Step 2: Apply RLS policies
    console.log('\n🔒 Step 2: Applying Row Level Security policies...');
    const rlsSQL = fs.readFileSync(
      path.join(__dirname, 'supabase/migrations/20241221000002_rls_policies.sql'),
      'utf8'
    );
    
    const { error: rlsError } = await supabase.rpc('exec_sql', { sql: rlsSQL });
    if (rlsError) {
      console.error('❌ RLS policies failed:', rlsError);
    } else {
      console.log('✅ Row Level Security policies applied');
    }

    // Step 3: Create storage buckets
    console.log('\n📁 Step 3: Creating storage buckets...');
    await createStorageBuckets();

    // Step 4: Create demo admin user
    console.log('\n👤 Step 4: Creating demo admin user...');
    await createDemoUsers();

    console.log('\n🎉 Setup completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Run: npm run dev');
    console.log('2. Open: http://localhost:3000');
    console.log('3. Login with: <EMAIL> / admin123');
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
  }
}

async function createStorageBuckets() {
  const buckets = [
    { name: 'patient-files', public: false },
    { name: 'xrays', public: false },
    { name: 'treatment-photos', public: false },
    { name: 'prescriptions', public: false },
    { name: 'avatars', public: true }
  ];

  for (const bucket of buckets) {
    const { error } = await supabase.storage.createBucket(bucket.name, {
      public: bucket.public,
      allowedMimeTypes: bucket.name === 'avatars' 
        ? ['image/jpeg', 'image/png', 'image/webp']
        : undefined,
      fileSizeLimit: bucket.name === 'xrays' ? 20971520 : 10485760 // 20MB for xrays, 10MB for others
    });

    if (error && !error.message.includes('already exists')) {
      console.error(`❌ Failed to create bucket ${bucket.name}:`, error);
    } else {
      console.log(`✅ Storage bucket '${bucket.name}' created`);
    }
  }
}

async function createDemoUsers() {
  // Create demo admin user
  const { data: adminUser, error: adminError } = await supabase.auth.admin.createUser({
    email: '<EMAIL>',
    password: 'admin123',
    email_confirm: true,
    user_metadata: {
      role: 'super_admin',
      full_name: 'Super Admin'
    }
  });

  if (adminError && !adminError.message.includes('already registered')) {
    console.error('❌ Failed to create admin user:', adminError);
  } else {
    console.log('✅ Demo admin user created: <EMAIL> / admin123');
  }

  // Create demo doctor user
  const { data: doctorUser, error: doctorError } = await supabase.auth.admin.createUser({
    email: '<EMAIL>',
    password: 'doctor123',
    email_confirm: true,
    user_metadata: {
      role: 'doctor',
      full_name: 'Dr. Demo Doctor'
    }
  });

  if (doctorError && !doctorError.message.includes('already registered')) {
    console.error('❌ Failed to create doctor user:', doctorError);
  } else {
    console.log('✅ Demo doctor user created: <EMAIL> / doctor123');
  }

  // Insert admin into platform_admins table
  if (adminUser?.user) {
    const { error: insertError } = await supabase
      .from('platform_admins')
      .insert({
        id: adminUser.user.id,
        email: '<EMAIL>',
        password_hash: '$2b$12$demo.hash.value',
        full_name: 'Super Admin',
        role: 'super_admin',
        is_active: true
      });

    if (insertError && !insertError.message.includes('duplicate')) {
      console.error('❌ Failed to insert admin record:', insertError);
    }
  }

  // Insert doctor into doctors table
  if (doctorUser?.user) {
    const { error: insertError } = await supabase
      .from('doctors')
      .insert({
        id: doctorUser.user.id,
        email: '<EMAIL>',
        password_hash: '$2b$12$demo.hash.value',
        full_name: 'Dr. Demo Doctor',
        license_number: 'LIC123456',
        clinic_name: 'Demo Dental Clinic',
        phone: '+**********',
        subscription_plan: 'premium',
        subscription_status: 'active',
        is_active: true
      });

    if (insertError && !insertError.message.includes('duplicate')) {
      console.error('❌ Failed to insert doctor record:', insertError);
    }
  }
}

// Run setup
setupDatabase();
