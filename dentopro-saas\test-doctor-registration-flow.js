// Test the complete doctor registration flow
const fs = require('fs');
const path = require('path');

console.log('🏥 Testing Doctor Registration Flow...\n');

// Test 1: Check if all required files exist
console.log('📋 Test 1: Required Files');

const requiredFiles = [
  'src/app/auth/doctor-registration/page.tsx',
  'src/app/api/auth/complete-doctor-registration/route.ts',
  'src/app/api/payments/create-subscription/route.ts',
  'src/app/payment/checkout/page.tsx',
  'src/app/doctor/dashboard/page.tsx',
  'src/lib/stripe.ts'
];

requiredFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} - EXISTS`);
  } else {
    console.log(`❌ ${file} - MISSING`);
  }
});

// Test 2: Check Stripe configuration
console.log('\n📋 Test 2: Stripe Configuration');

try {
  const stripePath = path.join(__dirname, 'src/lib/stripe.ts');
  const stripeContent = fs.readFileSync(stripePath, 'utf8');
  
  const stripeFeatures = [
    'SUBSCRIPTION_PLANS',
    'formatPrice',
    'calculateTrialEndDate',
    'getStripe'
  ];
  
  stripeFeatures.forEach(feature => {
    if (stripeContent.includes(feature)) {
      console.log(`✅ ${feature} - IMPLEMENTED`);
    } else {
      console.log(`❌ ${feature} - MISSING`);
    }
  });
} catch (err) {
  console.log('❌ Could not read Stripe configuration');
}

// Test 3: Check environment variables
console.log('\n📋 Test 3: Environment Variables');

require('dotenv').config({ path: '.env.local' });

const requiredEnvVars = [
  'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY',
  'STRIPE_SECRET_KEY',
  'NEXT_PUBLIC_APP_URL'
];

requiredEnvVars.forEach(varName => {
  const value = process.env[varName];
  if (value && !value.includes('your_') && !value.includes('_here')) {
    console.log(`✅ ${varName} - CONFIGURED`);
  } else {
    console.log(`⚠️ ${varName} - NEEDS REAL VALUE`);
  }
});

// Test 4: Check package.json for Stripe
console.log('\n📋 Test 4: Dependencies');

try {
  const packagePath = path.join(__dirname, 'package.json');
  const packageContent = fs.readFileSync(packagePath, 'utf8');
  const packageJson = JSON.parse(packageContent);
  
  const requiredDeps = ['stripe', '@stripe/stripe-js'];
  
  requiredDeps.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`✅ ${dep} - INSTALLED (${packageJson.dependencies[dep]})`);
    } else {
      console.log(`❌ ${dep} - NOT INSTALLED`);
    }
  });
} catch (err) {
  console.log('❌ Could not read package.json');
}

// Test 5: Check OAuth callback configuration
console.log('\n📋 Test 5: OAuth Callback');

try {
  const callbackPath = path.join(__dirname, 'src/app/auth/callback/route.ts');
  const callbackContent = fs.readFileSync(callbackPath, 'utf8');
  
  if (callbackContent.includes('doctor-registration')) {
    console.log('✅ OAuth callback redirects to doctor registration');
  } else {
    console.log('⚠️ OAuth callback may not redirect to doctor registration');
  }
  
  if (callbackContent.includes('new_doctor')) {
    console.log('✅ New doctor role handling implemented');
  } else {
    console.log('⚠️ New doctor role handling may be missing');
  }
} catch (err) {
  console.log('❌ Could not read OAuth callback');
}

console.log('\n🎯 Doctor Registration Flow Test Complete!');

console.log('\n📋 TESTING WORKFLOW:');
console.log('1. 🌐 Go to: http://localhost:3000');
console.log('2. 🔐 Click "Doctor Login"');
console.log('3. 🔗 Click "Continue with Google" (or other OAuth)');
console.log('4. 📝 Complete OAuth login');
console.log('5. 🏥 Fill doctor registration form:');
console.log('   - Name: "Dr. John Smith"');
console.log('   - License: "ML123456789"');
console.log('   - Clinic: "Smith Dental Clinic"');
console.log('   - Phone: "****** 567 8900"');
console.log('   - Plan: "7-Day Free Trial"');
console.log('6. ✅ Click "Start 7-Day Free Trial"');
console.log('7. 🎉 Should redirect to doctor dashboard');

console.log('\n💡 PAYMENT TESTING:');
console.log('- Trial plan: No payment required');
console.log('- Paid plans: Demo payment form (no real charges)');
console.log('- All plans include 7-day free trial');

console.log('\n🔧 SETUP NOTES:');
console.log('- Stripe keys are placeholder values (for demo)');
console.log('- Real Stripe setup needed for production');
console.log('- OAuth providers need to be configured in Supabase');
console.log('- Database subscription plans will be created automatically');
