// Supabase Storage utilities for file management
import { createClientSupabaseClient, supabaseAdmin } from './supabase';
import { useAuth } from '@/hooks/useAuth';

// Storage bucket names
export const STORAGE_BUCKETS = {
  PATIENT_FILES: 'patient-files',
  XRAYS: 'xrays',
  TREATMENT_PHOTOS: 'treatment-photos',
  PRESCRIPTIONS: 'prescriptions',
  DOCUMENTS: 'documents',
  AVATARS: 'avatars'
} as const;

// File type configurations
export const FILE_CONFIGS = {
  IMAGES: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
    extensions: ['.jpg', '.jpeg', '.png', '.webp', '.gif']
  },
  DOCUMENTS: {
    maxSize: 50 * 1024 * 1024, // 50MB
    allowedTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    extensions: ['.pdf', '.doc', '.docx']
  },
  XRAYS: {
    maxSize: 20 * 1024 * 1024, // 20MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/tiff', 'image/dicom'],
    extensions: ['.jpg', '.jpeg', '.png', '.tiff', '.dcm']
  }
} as const;

export interface UploadOptions {
  bucket: string;
  folder?: string;
  fileName?: string;
  upsert?: boolean;
  metadata?: Record<string, any>;
}

export interface UploadResult {
  path: string;
  fullPath: string;
  publicUrl: string;
  id: string;
}

export class SupabaseStorage {
  private supabase = createClientSupabaseClient();

  // Validate file before upload
  private validateFile(file: File, config: any): void {
    // Check file size
    if (file.size > config.maxSize) {
      throw new Error(`File size exceeds maximum allowed size of ${config.maxSize / (1024 * 1024)}MB`);
    }

    // Check file type
    if (!config.allowedTypes.includes(file.type as any)) {
      throw new Error(`File type ${file.type} is not allowed. Allowed types: ${config.allowedTypes.join(', ')}`);
    }

    // Check file extension
    const extension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!config.extensions.includes(extension as any)) {
      throw new Error(`File extension ${extension} is not allowed. Allowed extensions: ${config.extensions.join(', ')}`);
    }
  }

  // Generate unique file path
  private generateFilePath(
    doctorId: string,
    bucket: string,
    fileName: string,
    folder?: string
  ): string {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 8);
    const extension = fileName.split('.').pop();
    const baseName = fileName.split('.').slice(0, -1).join('.');
    const sanitizedBaseName = baseName.replace(/[^a-zA-Z0-9-_]/g, '_');
    
    const uniqueFileName = `${sanitizedBaseName}_${timestamp}_${randomString}.${extension}`;
    
    if (folder) {
      return `${doctorId}/${folder}/${uniqueFileName}`;
    }
    
    return `${doctorId}/${uniqueFileName}`;
  }

  // Upload file to Supabase Storage
  async uploadFile(
    file: File,
    options: UploadOptions
  ): Promise<UploadResult> {
    try {
      // Get current user context
      const { data: { user } } = await this.supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Get doctor ID for path generation
      const doctorId = user.user_metadata?.doctor_id || user.id;

      // Validate file based on bucket type
      let config;
      switch (options.bucket) {
        case STORAGE_BUCKETS.XRAYS:
          config = FILE_CONFIGS.XRAYS;
          break;
        case STORAGE_BUCKETS.TREATMENT_PHOTOS:
        case STORAGE_BUCKETS.AVATARS:
          config = FILE_CONFIGS.IMAGES;
          break;
        case STORAGE_BUCKETS.DOCUMENTS:
        case STORAGE_BUCKETS.PRESCRIPTIONS:
          config = FILE_CONFIGS.DOCUMENTS;
          break;
        default:
          config = FILE_CONFIGS.IMAGES;
      }

      this.validateFile(file, config);

      // Generate file path
      const filePath = this.generateFilePath(
        doctorId,
        options.bucket,
        options.fileName || file.name,
        options.folder
      );

      // Upload file
      const { data, error } = await this.supabase.storage
        .from(options.bucket)
        .upload(filePath, file, {
          upsert: options.upsert || false,
          metadata: options.metadata
        });

      if (error) {
        throw error;
      }

      // Get public URL
      const { data: urlData } = this.supabase.storage
        .from(options.bucket)
        .getPublicUrl(filePath);

      return {
        path: data.path,
        fullPath: data.fullPath,
        publicUrl: urlData.publicUrl,
        id: data.id
      };
    } catch (error) {
      console.error('File upload error:', error);
      throw error;
    }
  }

  // Upload multiple files
  async uploadMultipleFiles(
    files: File[],
    options: UploadOptions
  ): Promise<UploadResult[]> {
    const uploadPromises = files.map(file => 
      this.uploadFile(file, { ...options, fileName: file.name })
    );

    return Promise.all(uploadPromises);
  }

  // Download file
  async downloadFile(bucket: string, path: string): Promise<Blob> {
    const { data, error } = await this.supabase.storage
      .from(bucket)
      .download(path);

    if (error) {
      throw error;
    }

    return data;
  }

  // Get public URL for file
  getPublicUrl(bucket: string, path: string): string {
    const { data } = this.supabase.storage
      .from(bucket)
      .getPublicUrl(path);

    return data.publicUrl;
  }

  // Get signed URL for private files
  async getSignedUrl(
    bucket: string,
    path: string,
    expiresIn: number = 3600 // 1 hour default
  ): Promise<string> {
    const { data, error } = await this.supabase.storage
      .from(bucket)
      .createSignedUrl(path, expiresIn);

    if (error) {
      throw error;
    }

    return data.signedUrl;
  }

  // Delete file
  async deleteFile(bucket: string, path: string): Promise<void> {
    const { error } = await this.supabase.storage
      .from(bucket)
      .remove([path]);

    if (error) {
      throw error;
    }
  }

  // Delete multiple files
  async deleteMultipleFiles(bucket: string, paths: string[]): Promise<void> {
    const { error } = await this.supabase.storage
      .from(bucket)
      .remove(paths);

    if (error) {
      throw error;
    }
  }

  // List files in a folder
  async listFiles(
    bucket: string,
    folder?: string,
    options?: {
      limit?: number;
      offset?: number;
      sortBy?: { column: string; order: 'asc' | 'desc' };
    }
  ) {
    const { data, error } = await this.supabase.storage
      .from(bucket)
      .list(folder, {
        limit: options?.limit,
        offset: options?.offset,
        sortBy: options?.sortBy
      });

    if (error) {
      throw error;
    }

    return data;
  }

  // Move file to different location
  async moveFile(
    bucket: string,
    fromPath: string,
    toPath: string
  ): Promise<void> {
    const { error } = await this.supabase.storage
      .from(bucket)
      .move(fromPath, toPath);

    if (error) {
      throw error;
    }
  }

  // Copy file
  async copyFile(
    bucket: string,
    fromPath: string,
    toPath: string
  ): Promise<void> {
    const { error } = await this.supabase.storage
      .from(bucket)
      .copy(fromPath, toPath);

    if (error) {
      throw error;
    }
  }
}

// Storage management for admin operations
export class SupabaseStorageAdmin {
  private supabase = supabaseAdmin;

  // Create storage bucket
  async createBucket(
    name: string,
    options?: {
      public?: boolean;
      allowedMimeTypes?: string[];
      fileSizeLimit?: number;
    }
  ) {
    const { data, error } = await this.supabase.storage.createBucket(name, {
      public: options?.public || false,
      allowedMimeTypes: options?.allowedMimeTypes,
      fileSizeLimit: options?.fileSizeLimit
    });

    if (error) {
      throw error;
    }

    return data;
  }

  // Delete bucket
  async deleteBucket(name: string) {
    const { error } = await this.supabase.storage.deleteBucket(name);

    if (error) {
      throw error;
    }
  }

  // Get bucket details
  async getBucket(name: string) {
    const { data, error } = await this.supabase.storage.getBucket(name);

    if (error) {
      throw error;
    }

    return data;
  }

  // List all buckets
  async listBuckets() {
    const { data, error } = await this.supabase.storage.listBuckets();

    if (error) {
      throw error;
    }

    return data;
  }

  // Update bucket settings
  async updateBucket(
    name: string,
    options: {
      public?: boolean;
      allowedMimeTypes?: string[];
      fileSizeLimit?: number;
    }
  ) {
    const { data, error } = await this.supabase.storage.updateBucket(name, {
      public: options.public || false,
      allowedMimeTypes: options.allowedMimeTypes,
      fileSizeLimit: options.fileSizeLimit
    });

    if (error) {
      throw error;
    }

    return data;
  }
}

// Utility functions for common file operations
export const FileUtils = {
  // Format file size for display
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // Get file extension
  getFileExtension: (filename: string): string => {
    return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
  },

  // Check if file is image
  isImageFile: (filename: string): boolean => {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
    const extension = FileUtils.getFileExtension(filename).toLowerCase();
    return imageExtensions.includes(extension);
  },

  // Generate thumbnail URL (if using image transformation service)
  getThumbnailUrl: (publicUrl: string, width: number = 200, height: number = 200): string => {
    // This would depend on your image transformation service
    // For now, return the original URL
    return publicUrl;
  },

  // Validate file name
  sanitizeFileName: (filename: string): string => {
    return filename.replace(/[^a-zA-Z0-9.-]/g, '_');
  }
};

// Export storage instance
export const storage = new SupabaseStorage();
export const storageAdmin = new SupabaseStorageAdmin();

export default storage;
