# Application Configuration
NEXT_PUBLIC_APP_NAME=DentoPro
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_VERSION=1.0.0

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://mjurshygvtfntjipemin.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1qdXJzaHlndnRmbnRqaXBlbWluIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMxMDM0NTUsImV4cCI6MjA2ODY3OTQ1NX0.MeBN_BQMpAFPWzwVTvc2N2klfWislsPxwIscpdu071k
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1qdXJzaHlndnRmbnRqaXBlbWluIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzEwMzQ1NSwiZXhwIjoyMDY4Njc5NDU1fQ.EF3dx-qT--Q0MJcd3UaL1ury0ODI3cu83R7oh81Q2h0
SUPABASE_JWT_SECRET=KI3aRtJWhrLniRqEuv8RA5YqKFcGM68s6Y48YBp6Mjan0tqTWcIE21/HApg1TJ5PY8XO2CU6Em8rXkyI3fcICQ==
SUPABASE_DB_PASSWORD=01066882723abc

# Supabase Database Direct Connection
SUPABASE_DB_HOST=db.mjurshygvtfntjipemin.supabase.co
SUPABASE_DB_PORT=5432
SUPABASE_DB_NAME=postgres
SUPABASE_DB_USER=postgres

# Authentication
NEXTAUTH_SECRET=your-nextauth-secret-here-change-in-production
NEXTAUTH_URL=http://localhost:3000

# OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id-here
GOOGLE_CLIENT_SECRET=your-google-client-secret-here

# Two-Factor Authentication
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=your-twilio-phone-number

# Encryption (for HIPAA compliance)
ENCRYPTION_KEY=a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456

# Stripe Configuration (for subscriptions)
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# Twilio Configuration (for WhatsApp notifications)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# File Upload Configuration
MAX_FILE_SIZE=50MB
ALLOWED_FILE_TYPES=pdf,doc,docx,jpg,jpeg,png,gif,webp

# Development Settings
NODE_ENV=development
LOG_LEVEL=debug
