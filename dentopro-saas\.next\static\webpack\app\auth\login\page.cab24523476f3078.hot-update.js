"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useAuth */ \"(app-pages-browser)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/constants */ \"(app-pages-browser)/./src/lib/constants.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction LoginPage() {\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { login, loginWithGoogle, isAuthenticated, user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const redirectTo = searchParams.get('redirectTo') || '/dashboard';\n    const demoType = searchParams.get('demo');\n    // Redirect if already authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            if (isAuthenticated && user) {\n                const defaultRedirect = user.role === 'super_admin' ? '/admin' : user.role === 'doctor' ? '/dashboard' : '/patient';\n                router.push(redirectTo || defaultRedirect);\n            }\n        }\n    }[\"LoginPage.useEffect\"], [\n        isAuthenticated,\n        user,\n        router,\n        redirectTo\n    ]);\n    const handleEmailLogin = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError('');\n        try {\n            const result = await login(email, password);\n            if (result.error) {\n                setError(result.error.message);\n            } else {\n            // Redirect will happen automatically via useEffect\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Login failed');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleGoogleLogin = async ()=>{\n        setLoading(true);\n        setError('');\n        try {\n            const result = await loginWithGoogle();\n            if (result.error) {\n                setError(result.error.message);\n            }\n        // Redirect will happen automatically after OAuth callback\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Google login failed');\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8 p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-blue-600 mb-2\",\n                            children: _lib_constants__WEBPACK_IMPORTED_MODULE_6__.APP_NAME\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-gray-900\",\n                            children: \"Sign in to your account\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-gray-600\",\n                            children: [\n                                \"Or\",\n                                ' ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/auth/register\",\n                                    className: \"font-medium text-blue-600 hover:text-blue-500\",\n                                    children: \"create a new account\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6\",\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4 p-3 bg-red-50 border border-red-200 rounded-md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-red-600\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleEmailLogin,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"email\",\n                                            name: \"email\",\n                                            type: \"email\",\n                                            autoComplete: \"email\",\n                                            required: true,\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value),\n                                            className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"Enter your email\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"password\",\n                                            name: \"password\",\n                                            type: \"password\",\n                                            autoComplete: \"current-password\",\n                                            required: true,\n                                            value: password,\n                                            onChange: (e)=>setPassword(e.target.value),\n                                            className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                            placeholder: \"Enter your password\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"remember-me\",\n                                                    name: \"remember-me\",\n                                                    type: \"checkbox\",\n                                                    className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"remember-me\",\n                                                    className: \"ml-2 block text-sm text-gray-900\",\n                                                    children: \"Remember me\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/auth/reset-password\",\n                                                className: \"font-medium text-blue-600 hover:text-blue-500\",\n                                                children: \"Forgot your password?\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"submit\",\n                                    className: \"w-full\",\n                                    disabled: loading,\n                                    children: loading ? 'Signing in...' : 'Sign in'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full border-t border-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex justify-center text-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 bg-white text-gray-500\",\n                                            children: \"Or continue with\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                type: \"button\",\n                                variant: \"outline\",\n                                className: \"w-full\",\n                                onClick: handleGoogleLogin,\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 mr-2\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fill: \"currentColor\",\n                                                d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fill: \"currentColor\",\n                                                d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fill: \"currentColor\",\n                                                d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fill: \"currentColor\",\n                                                d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this),\n                                    loading ? 'Connecting...' : 'Sign in with Google'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-yellow-800 mb-2\",\n                            children: \"Demo Accounts\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-yellow-700 space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Super Admin:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 16\n                                        }, this),\n                                        \" <EMAIL> / admin123\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Doctor:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 16\n                                        }, this),\n                                        \" <EMAIL> / doctor123\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Patient:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 16\n                                        }, this),\n                                        \" <EMAIL> / patient123\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center text-sm text-gray-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"By signing in, you agree to our\",\n                            ' ',\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/terms\",\n                                className: \"text-blue-600 hover:text-blue-500\",\n                                children: \"Terms of Service\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this),\n                            ' ',\n                            \"and\",\n                            ' ',\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/privacy\",\n                                className: \"text-blue-600 hover:text-blue-500\",\n                                children: \"Privacy Policy\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"sfBEOFUmQOSWVyDDueJLwX/2P7A=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/login/page.tsx\n"));

/***/ })

});