import { Button } from '@/components/ui/button';
import Link from 'next/link';

export default function DemoPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/">
                <h1 className="text-2xl font-bold text-blue-600 cursor-pointer">DentoPro</h1>
              </Link>
              <span className="ml-2 text-sm text-gray-500">Demo</span>
            </div>
            <div className="flex space-x-4">
              <Link href="/auth/login">
                <Button variant="outline">Login</Button>
              </Link>
              <Link href="/auth/register">
                <Button>Sign Up</Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Experience DentoPro Demo
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Explore our comprehensive dental clinic management system with sample data.
            See how DentoPro can transform your practice.
          </p>
        </div>

        {/* Demo Options */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Super Admin Demo */}
          <div className="bg-white p-6 rounded-lg shadow-md border-2 border-purple-200">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4 mx-auto">
              <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Super Admin Dashboard</h3>
            <p className="text-gray-600 mb-4">
              Platform management, analytics, user management, and system monitoring.
            </p>
            <div className="space-y-2 mb-4">
              <p className="text-sm text-gray-500">Demo Credentials:</p>
              <p className="text-sm font-mono bg-gray-100 p-2 rounded">
                Email: <EMAIL><br/>
                Password: admin123
              </p>
            </div>
            <Link href="/auth/login?demo=admin">
              <Button className="w-full bg-purple-600 hover:bg-purple-700">
                Try Admin Demo
              </Button>
            </Link>
          </div>

          {/* Doctor Demo */}
          <div className="bg-white p-6 rounded-lg shadow-md border-2 border-blue-200">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4 mx-auto">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Doctor Dashboard</h3>
            <p className="text-gray-600 mb-4">
              Patient management, appointments, treatments, prescriptions, and clinic operations.
            </p>
            <div className="space-y-2 mb-4">
              <p className="text-sm text-gray-500">Demo Credentials:</p>
              <p className="text-sm font-mono bg-gray-100 p-2 rounded">
                Email: <EMAIL><br/>
                Password: doctor123
              </p>
            </div>
            <Link href="/auth/login?demo=doctor">
              <Button className="w-full">
                Try Doctor Demo
              </Button>
            </Link>
          </div>

          {/* Patient Demo */}
          <div className="bg-white p-6 rounded-lg shadow-md border-2 border-green-200">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4 mx-auto">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Patient Portal</h3>
            <p className="text-gray-600 mb-4">
              Book appointments, view treatment history, access prescriptions, and communicate with doctors.
            </p>
            <div className="space-y-2 mb-4">
              <p className="text-sm text-gray-500">Demo Credentials:</p>
              <p className="text-sm font-mono bg-gray-100 p-2 rounded">
                Email: <EMAIL><br/>
                Password: patient123
              </p>
            </div>
            <Link href="/auth/login?demo=patient">
              <Button className="w-full bg-green-600 hover:bg-green-700">
                Try Patient Demo
              </Button>
            </Link>
          </div>
        </div>

        {/* Features Preview */}
        <div className="mt-16 bg-white rounded-lg shadow-lg p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">What You'll See in the Demo</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">🏥 Clinic Management</h4>
              <ul className="space-y-2 text-gray-600">
                <li>• Complete patient database with medical history</li>
                <li>• Smart appointment scheduling system</li>
                <li>• Digital treatment records and charts</li>
                <li>• Inventory management with alerts</li>
                <li>• Financial reporting and analytics</li>
              </ul>
            </div>
            
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">🔧 Advanced Features</h4>
              <ul className="space-y-2 text-gray-600">
                <li>• Multi-language support (EN/AR)</li>
                <li>• WhatsApp integration for reminders</li>
                <li>• QR code prescriptions</li>
                <li>• HIPAA/GDPR compliant security</li>
                <li>• Mobile-responsive design</li>
              </ul>
            </div>
          </div>

          <div className="mt-8 text-center">
            <p className="text-gray-600 mb-4">
              Ready to start your own clinic management system?
            </p>
            <Link href="/auth/register">
              <Button size="lg" className="px-8">
                Start Your Free Trial
              </Button>
            </Link>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-50 border-t mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-sm text-gray-500">
            <p>This is a demo environment with sample data. No real patient information is stored.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
