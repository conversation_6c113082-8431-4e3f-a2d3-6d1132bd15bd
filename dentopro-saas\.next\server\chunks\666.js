exports.id=666,exports.ids=[666],exports.modules={324:(a,b,c)=>{Promise.resolve().then(c.bind(c,1188)),Promise.resolve().then(c.bind(c,7445))},440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(1658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},1135:()=>{},1188:(a,b,c)=>{"use strict";c.d(b,{ThemeProvider:()=>g});var d=c(687),e=c(3210);let f=(0,e.createContext)({theme:"system",setTheme:()=>null});function g({children:a,defaultTheme:b="system",storageKey:c="dentopro-ui-theme",...g}){let[h,i]=(0,e.useState)(()=>localStorage?.getItem(c)||b);return(0,d.jsx)(f.Provider,{...g,value:{theme:h,setTheme:a=>{localStorage?.setItem(c,a),i(a)}},children:a})}},2670:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,5227,23)),Promise.resolve().then(c.t.bind(c,6346,23)),Promise.resolve().then(c.t.bind(c,7924,23)),Promise.resolve().then(c.t.bind(c,99,23)),Promise.resolve().then(c.t.bind(c,8243,23)),Promise.resolve().then(c.t.bind(c,8827,23)),Promise.resolve().then(c.t.bind(c,2763,23)),Promise.resolve().then(c.t.bind(c,7173,23)),Promise.resolve().then(c.bind(c,5587))},4292:(a,b,c)=>{Promise.resolve().then(c.bind(c,8462)),Promise.resolve().then(c.bind(c,9567))},4431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>m,metadata:()=>l});var d=c(7413),e=c(2376),f=c.n(e),g=c(8726),h=c.n(g);c(1135);var i=c(9567),j=c(8462),k=c(6890);let l={title:`${k.C3} - Complete Dental Clinic Management`,description:"Streamline your dental practice with our comprehensive SaaS platform. Manage patients, appointments, treatments, inventory, and more - all in one place.",keywords:"dental clinic management, dental software, patient management, appointment scheduling, dental SaaS",authors:[{name:"DentoPro Team"}],viewport:"width=device-width, initial-scale=1",robots:"index, follow",openGraph:{title:`${k.C3} - Complete Dental Clinic Management`,description:"Streamline your dental practice with our comprehensive SaaS platform.",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:`${k.C3} - Complete Dental Clinic Management`,description:"Streamline your dental practice with our comprehensive SaaS platform."}};function m({children:a}){return(0,d.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:(0,d.jsx)(j.ThemeProvider,{defaultTheme:"system",storageKey:"dentopro-ui-theme",children:(0,d.jsx)(i.AuthProvider,{children:a})})})})}},6391:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31mx\x1b[0m You're importing a component that needs \"next/headers\". That only works in a Server Component which is not supported in the pages/ directory. Read more: https://nextjs.org/docs/app/building-your-application/rendering/server-components\n  \x1b[31m|\x1b[0m\n\n   ,-[\x1b[36;1;4mD:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\lib\\supabase.ts\x1b[0m:4:1]\n \x1b[2m1\x1b[0m | // Supabase client configuration for DentoPro SaaS platform\n \x1b[2m2\x1b[0m | import { createClient } from '@supabase/supabase-js';\n \x1b[2m3\x1b[0m | import { createServerClient, createBrowserClient } from '@supabase/ssr';\n \x1b[2m4\x1b[0m | import { cookies } from 'next/headers';\n   : \x1b[35;1m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\x1b[0m\n \x1b[2m5\x1b[0m | \n \x1b[2m6\x1b[0m | // Database type definitions (will be generated from Supabase)\n \x1b[2m7\x1b[0m | export interface Database {\n   `----\n")},6582:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,6133,23)),Promise.resolve().then(c.t.bind(c,6444,23)),Promise.resolve().then(c.t.bind(c,6042,23)),Promise.resolve().then(c.t.bind(c,9477,23)),Promise.resolve().then(c.t.bind(c,9345,23)),Promise.resolve().then(c.t.bind(c,2089,23)),Promise.resolve().then(c.t.bind(c,6577,23)),Promise.resolve().then(c.t.bind(c,1307,23)),Promise.resolve().then(c.t.bind(c,4817,23))},6890:(a,b,c)=>{"use strict";c.d(b,{C3:()=>d,P:()=>f,VK:()=>e});let d="DentoPro",e={USD:{code:"USD",symbol:"$",name:"US Dollar",locale:"en-US"},EGP:{code:"EGP",symbol:"ج.م",name:"Egyptian Pound",locale:"ar-EG"},EUR:{code:"EUR",symbol:"€",name:"Euro",locale:"en-EU"}},f={SOFTWARE_WATERMARK:`\xa9 ${new Date().getFullYear()} DentoPro - Licensed to Single Doctor Use Only`,DOMAIN_BINDING_REQUIRED:!0,MAX_CONCURRENT_SESSIONS:3,USAGE_TRACKING_ENABLED:!0,ANTI_RESALE_PROTECTION:!0}},7445:(a,b,c)=>{"use strict";c.d(b,{A:()=>j,AuthProvider:()=>i});var d=c(687),e=c(3210),f=c(8259),g=c(6391);let h=(0,e.createContext)(void 0);function i({children:a}){let[b,c]=(0,e.useState)(null),[i,j]=(0,e.useState)(null),[k,l]=(0,e.useState)(!0),[m,n]=(0,e.useState)(null),[o,p]=(0,e.useState)(null);(0,g.createClientSupabaseClient)();let q=a=>b?.role===a,r={user:b,session:i,loading:k,login:async(a,b)=>{l(!0);try{let c=await g.SupabaseUtils.signInWithPassword(a,b);if(c.error)throw c.error;return c}catch(a){throw console.error("Login failed:",a),a}finally{l(!1)}},loginWithGoogle:async()=>{l(!0);try{let a=await g.SupabaseUtils.signInWithGoogle();if(a.error)throw a.error;return a}catch(a){throw console.error("Google login failed:",a),a}finally{l(!1)}},logout:async()=>{l(!0);try{let{error:a}=await g.SupabaseUtils.signOut();if(a)throw a}catch(a){throw console.error("Logout failed:",a),a}finally{l(!1)}},register:async a=>{l(!0);try{let b=await g.SupabaseUtils.signUp(a.email,a.password,{role:a.role,...a.metadata});if(b.error)throw b.error;return b}catch(a){throw console.error("Registration failed:",a),a}finally{l(!1)}},resetPassword:async a=>{try{let b=await g.SupabaseUtils.resetPassword(a);if(b.error)throw b.error;return b}catch(a){throw console.error("Password reset failed:",a),a}},isAuthenticated:!!b,hasRole:q,isSuperAdmin:q(f.gG.SUPER_ADMIN),isDoctor:q(f.gG.DOCTOR),isPatient:q(f.gG.PATIENT),doctorId:m,patientId:o};return(0,d.jsx)(h.Provider,{value:r,children:a})}function j(){let a=(0,e.useContext)(h);if(void 0===a)throw Error("useAuth must be used within an AuthProvider");return a}},8259:(a,b,c)=>{"use strict";c.d(b,{Sj:()=>e,gG:()=>d});var d=function(a){return a.SUPER_ADMIN="super_admin",a.DOCTOR="doctor",a.PATIENT="patient",a}({}),e=function(a){return a.USD="USD",a.EGP="EGP",a.EUR="EUR",a}({})},8462:(a,b,c)=>{"use strict";c.d(b,{ThemeProvider:()=>e});var d=c(1369);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\contexts\\ThemeContext.tsx","ThemeProvider");(0,d.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\contexts\\ThemeContext.tsx","useTheme")},9567:(a,b,c)=>{"use strict";c.d(b,{AuthProvider:()=>e});var d=c(1369);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\hooks\\useAuth.tsx","AuthProvider");(0,d.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\hooks\\useAuth.tsx","useAuth")}};