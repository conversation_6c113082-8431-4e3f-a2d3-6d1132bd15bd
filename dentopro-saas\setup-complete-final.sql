-- FINAL COMPLETE DentoPro SaaS Database Setup
-- This script creates ALL tables needed for the complete application
-- Run this in your Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =============================================
-- CORE PLATFORM TABLES
-- =============================================

-- Create platform_admins table
CREATE TABLE IF NOT EXISTS platform_admins (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'super_admin',
    permissions JSONB DEFAULT '{}',
    last_login TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create doctors table
CREATE TABLE IF NOT EXISTS doctors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    license_number VARCHAR(100) UNIQUE NOT NULL,
    clinic_name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    whatsapp VARCHAR(20),
    subscription_plan VARCHAR(50) NOT NULL DEFAULT 'trial',
    subscription_status VARCHAR(50) NOT NULL DEFAULT 'trial',
    domain VARCHAR(255),
    trial_end_date TIMESTAMP WITH TIME ZONE,
    approved_by UUID REFERENCES platform_admins(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    last_login TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- SUBSCRIPTION & BILLING TABLES
-- =============================================

-- Create subscription_plans table
CREATE TABLE IF NOT EXISTS subscription_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price_monthly DECIMAL(10,2) NOT NULL,
    price_yearly DECIMAL(10,2),
    max_patients INTEGER,
    max_appointments_per_month INTEGER,
    features JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create subscriptions table
CREATE TABLE IF NOT EXISTS subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    plan_id UUID NOT NULL REFERENCES subscription_plans(id),
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    billing_cycle VARCHAR(20) NOT NULL DEFAULT 'monthly',
    current_period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    current_period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    trial_end TIMESTAMP WITH TIME ZONE,
    stripe_subscription_id VARCHAR(255),
    stripe_customer_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- CLINIC CONFIGURATION TABLES
-- =============================================

-- Create clinic_settings table (Enhanced version)
CREATE TABLE IF NOT EXISTS clinic_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    timezone VARCHAR(50) DEFAULT 'UTC',
    language VARCHAR(5) DEFAULT 'en',
    currency VARCHAR(3) DEFAULT 'USD',
    working_hours JSONB NOT NULL DEFAULT '{}',
    appointment_duration INTEGER DEFAULT 30,
    allow_online_booking BOOLEAN DEFAULT true,
    require_patient_verification BOOLEAN DEFAULT true,
    auto_reminders BOOLEAN DEFAULT true,
    whatsapp_notifications BOOLEAN DEFAULT false,
    email_notifications BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(doctor_id)
);

-- =============================================
-- PATIENT & CLINICAL TABLES
-- =============================================

-- Create patients table
CREATE TABLE IF NOT EXISTS patients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    email VARCHAR(255),
    full_name VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    date_of_birth DATE,
    gender VARCHAR(10),
    address TEXT,
    emergency_contact_name VARCHAR(255),
    emergency_contact_phone VARCHAR(20),
    emergency_contact_relationship VARCHAR(100),
    medical_history JSONB DEFAULT '{}',
    dental_history JSONB DEFAULT '{}',
    allergies TEXT[],
    current_medications TEXT[],
    insurance_info JSONB DEFAULT '{}',
    is_verified BOOLEAN DEFAULT false,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(doctor_id, phone)
);

-- Create patient_verification table
CREATE TABLE IF NOT EXISTS patient_verification (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id UUID NOT NULL REFERENCES patients(id) ON DELETE CASCADE,
    verification_code VARCHAR(10) NOT NULL,
    verification_type VARCHAR(50) NOT NULL DEFAULT 'phone',
    is_verified BOOLEAN DEFAULT false,
    verified_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    attempts INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create appointments table
CREATE TABLE IF NOT EXISTS appointments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    patient_id UUID NOT NULL REFERENCES patients(id) ON DELETE CASCADE,
    appointment_date DATE NOT NULL,
    appointment_time TIME NOT NULL,
    duration INTEGER DEFAULT 30,
    appointment_type VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'scheduled',
    notes TEXT,
    reminder_sent BOOLEAN DEFAULT false,
    source VARCHAR(50) DEFAULT 'doctor',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create treatments table
CREATE TABLE IF NOT EXISTS treatments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    patient_id UUID NOT NULL REFERENCES patients(id) ON DELETE CASCADE,
    appointment_id UUID REFERENCES appointments(id) ON DELETE SET NULL,
    procedure_name VARCHAR(255) NOT NULL,
    tooth_number VARCHAR(10),
    surface VARCHAR(50),
    diagnosis TEXT,
    treatment_plan TEXT,
    notes TEXT,
    cost DECIMAL(10,2),
    materials_used JSONB DEFAULT '[]',
    before_images TEXT[],
    after_images TEXT[],
    pain_score INTEGER CHECK (pain_score >= 0 AND pain_score <= 10),
    follow_up_required BOOLEAN DEFAULT false,
    follow_up_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create prescriptions table
CREATE TABLE IF NOT EXISTS prescriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    patient_id UUID NOT NULL REFERENCES patients(id) ON DELETE CASCADE,
    treatment_id UUID REFERENCES treatments(id) ON DELETE SET NULL,
    prescription_number VARCHAR(50) UNIQUE NOT NULL,
    medications JSONB NOT NULL DEFAULT '[]',
    instructions TEXT,
    qr_code TEXT,
    status VARCHAR(50) DEFAULT 'active',
    issued_date DATE NOT NULL DEFAULT CURRENT_DATE,
    expiry_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- BILLING & INVOICE TABLES
-- =============================================

-- Create invoices table
CREATE TABLE IF NOT EXISTS invoices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    patient_id UUID REFERENCES patients(id) ON DELETE SET NULL,
    subscription_id UUID REFERENCES subscriptions(id) ON DELETE SET NULL,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    invoice_type VARCHAR(50) NOT NULL DEFAULT 'treatment',
    amount DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status VARCHAR(50) DEFAULT 'pending',
    due_date DATE,
    paid_at TIMESTAMP WITH TIME ZONE,
    payment_method VARCHAR(50),
    stripe_invoice_id VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- INVENTORY MANAGEMENT TABLES
-- =============================================

-- Create materials table
CREATE TABLE IF NOT EXISTS materials (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    category VARCHAR(100),
    brand VARCHAR(100),
    unit VARCHAR(50) NOT NULL,
    cost_per_unit DECIMAL(10,2),
    current_stock INTEGER DEFAULT 0,
    minimum_stock INTEGER DEFAULT 0,
    expiry_date DATE,
    supplier_info JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create stock_logs table
CREATE TABLE IF NOT EXISTS stock_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    material_id UUID NOT NULL REFERENCES materials(id) ON DELETE CASCADE,
    treatment_id UUID REFERENCES treatments(id) ON DELETE SET NULL,
    transaction_type VARCHAR(50) NOT NULL,
    quantity INTEGER NOT NULL,
    previous_stock INTEGER NOT NULL,
    new_stock INTEGER NOT NULL,
    cost_per_unit DECIMAL(10,2),
    total_cost DECIMAL(10,2),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- SYSTEM & SECURITY TABLES
-- =============================================

-- Create notifications table
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    recipient_type VARCHAR(50) NOT NULL,
    recipient_id UUID NOT NULL,
    doctor_id UUID REFERENCES doctors(id) ON DELETE CASCADE,
    type VARCHAR(100) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    priority VARCHAR(20) DEFAULT 'normal',
    metadata JSONB DEFAULT '{}',
    scheduled_for TIMESTAMP WITH TIME ZONE,
    sent_at TIMESTAMP WITH TIME ZONE,
    read_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create file_uploads table
CREATE TABLE IF NOT EXISTS file_uploads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    patient_id UUID REFERENCES patients(id) ON DELETE CASCADE,
    appointment_id UUID REFERENCES appointments(id) ON DELETE SET NULL,
    treatment_id UUID REFERENCES treatments(id) ON DELETE SET NULL,
    file_name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    file_size BIGINT NOT NULL,
    file_path TEXT NOT NULL,
    file_url TEXT,
    category VARCHAR(100),
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    uploaded_by UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create license_tracking table
CREATE TABLE IF NOT EXISTS license_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    domain VARCHAR(255) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    last_active TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    usage_stats JSONB DEFAULT '{}',
    violations JSONB DEFAULT '[]',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create audit_logs table
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID,
    user_type VARCHAR(50) NOT NULL,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100),
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create platform_analytics table
CREATE TABLE IF NOT EXISTS platform_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    metric_type VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,2) NOT NULL,
    dimensions JSONB DEFAULT '{}',
    doctor_id UUID REFERENCES doctors(id) ON DELETE CASCADE,
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create licenses table (Enhanced)
CREATE TABLE IF NOT EXISTS licenses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    license_key VARCHAR(255) UNIQUE NOT NULL,
    domain VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'active',
    issued_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    last_verified TIMESTAMP WITH TIME ZONE,
    verification_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- ENABLE ROW LEVEL SECURITY
-- =============================================

ALTER TABLE platform_admins ENABLE ROW LEVEL SECURITY;
ALTER TABLE doctors ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE clinic_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE patients ENABLE ROW LEVEL SECURITY;
ALTER TABLE patient_verification ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE treatments ENABLE ROW LEVEL SECURITY;
ALTER TABLE prescriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE stock_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE file_uploads ENABLE ROW LEVEL SECURITY;
ALTER TABLE license_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE platform_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE licenses ENABLE ROW LEVEL SECURITY;

-- =============================================
-- CREATE PERFORMANCE INDEXES
-- =============================================

-- Core entity indexes
CREATE INDEX IF NOT EXISTS idx_patients_doctor_id ON patients(doctor_id);
CREATE INDEX IF NOT EXISTS idx_patients_phone ON patients(phone);
CREATE INDEX IF NOT EXISTS idx_patients_email ON patients(email);

-- Appointment indexes
CREATE INDEX IF NOT EXISTS idx_appointments_doctor_date ON appointments(doctor_id, appointment_date);
CREATE INDEX IF NOT EXISTS idx_appointments_patient ON appointments(patient_id);
CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);

-- Treatment indexes
CREATE INDEX IF NOT EXISTS idx_treatments_doctor_patient ON treatments(doctor_id, patient_id);
CREATE INDEX IF NOT EXISTS idx_treatments_appointment ON treatments(appointment_id);

-- Billing indexes
CREATE INDEX IF NOT EXISTS idx_invoices_doctor_status ON invoices(doctor_id, status);
CREATE INDEX IF NOT EXISTS idx_invoices_patient ON invoices(patient_id);

-- Prescription indexes
CREATE INDEX IF NOT EXISTS idx_prescriptions_doctor_patient ON prescriptions(doctor_id, patient_id);
CREATE INDEX IF NOT EXISTS idx_prescriptions_treatment ON prescriptions(treatment_id);

-- Inventory indexes
CREATE INDEX IF NOT EXISTS idx_materials_doctor_category ON materials(doctor_id, category);
CREATE INDEX IF NOT EXISTS idx_materials_low_stock ON materials(doctor_id, current_stock, minimum_stock);
CREATE INDEX IF NOT EXISTS idx_stock_logs_material ON stock_logs(material_id);

-- System indexes
CREATE INDEX IF NOT EXISTS idx_notifications_recipient ON notifications(recipient_type, recipient_id, status);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_action ON audit_logs(user_id, action);
CREATE INDEX IF NOT EXISTS idx_licenses_doctor_domain ON licenses(doctor_id, domain);
CREATE INDEX IF NOT EXISTS idx_file_uploads_patient ON file_uploads(patient_id);

-- =============================================
-- INSERT DEMO DATA
-- =============================================

-- Insert demo subscription plans
INSERT INTO subscription_plans (
    id, name, description, price_monthly, price_yearly, max_patients, max_appointments_per_month, features
) VALUES
(
    '11111111-1111-1111-1111-111111111111'::uuid,
    'Trial', '14-day free trial', 0.00, 0.00, 10, 50,
    '{"whatsapp": false, "sms": false, "advanced_reports": false}'
),
(
    '*************-2222-2222-************'::uuid,
    'Basic', 'Perfect for small clinics', 29.99, 299.99, 100, 500,
    '{"whatsapp": true, "sms": false, "advanced_reports": false}'
),
(
    '*************-3333-3333-************'::uuid,
    'Premium', 'Full-featured plan for growing clinics', 59.99, 599.99, 500, 2000,
    '{"whatsapp": true, "sms": true, "advanced_reports": true}'
) ON CONFLICT (id) DO NOTHING;

-- Insert demo admin user
INSERT INTO platform_admins (
    id, email, password_hash, full_name, role, is_active
) VALUES (
    'a1b2c3d4-e5f6-7890-1234-567890abcdef'::uuid,
    '<EMAIL>', '$2b$12$demo.hash.value', 'Super Admin', 'super_admin', true
) ON CONFLICT (email) DO NOTHING;

-- Insert demo doctor user
INSERT INTO doctors (
    id, email, password_hash, full_name, license_number, clinic_name, phone, subscription_plan, subscription_status, is_active
) VALUES (
    'b2c3d4e5-f6a7-8901-2345-678901bcdefb'::uuid,
    '<EMAIL>', '$2b$12$demo.hash.value', 'Dr. Demo Doctor', 'LIC123456', 'Demo Dental Clinic', '+**********', 'premium', 'active', true
) ON CONFLICT (email) DO NOTHING;

-- Insert clinic settings for demo doctor
INSERT INTO clinic_settings (
    doctor_id, timezone, language, currency, working_hours, appointment_duration, allow_online_booking
) VALUES (
    'b2c3d4e5-f6a7-8901-2345-678901bcdefb'::uuid,
    'America/New_York', 'en', 'USD',
    '{"monday": {"start": "09:00", "end": "17:00"}, "tuesday": {"start": "09:00", "end": "17:00"}, "wednesday": {"start": "09:00", "end": "17:00"}, "thursday": {"start": "09:00", "end": "17:00"}, "friday": {"start": "09:00", "end": "17:00"}}',
    30, true
) ON CONFLICT (doctor_id) DO NOTHING;

-- Insert demo patients
INSERT INTO patients (
    id, doctor_id, full_name, phone, email, date_of_birth, gender, address, medical_history, dental_history, is_verified
) VALUES
(
    'c3d4e5f6-a7b8-9012-3456-789012cdefab'::uuid,
    'b2c3d4e5-f6a7-8901-2345-678901bcdefb'::uuid,
    'John Smith', '+**********', '<EMAIL>', '1985-06-15', 'male',
    '123 Main St, City, State 12345',
    '{"conditions": ["hypertension"], "medications": ["lisinopril"]}',
    '{"last_cleaning": "2024-06-01", "cavities": 2}', true
),
(
    'd4e5f6a7-b8c9-0123-4567-890123defabc'::uuid,
    'b2c3d4e5-f6a7-8901-2345-678901bcdefb'::uuid,
    'Sarah Johnson', '+**********', '<EMAIL>', '1990-03-22', 'female',
    '456 Oak Ave, City, State 12345',
    '{"conditions": [], "medications": []}',
    '{"last_cleaning": "2024-05-15", "orthodontics": "completed"}', true
) ON CONFLICT (doctor_id, phone) DO NOTHING;

-- Insert demo appointments
INSERT INTO appointments (
    id, doctor_id, patient_id, appointment_date, appointment_time, appointment_type, status, notes
) VALUES
(
    'e5f6a7b8-c9d0-1234-5678-901234efabcd'::uuid,
    'b2c3d4e5-f6a7-8901-2345-678901bcdefb'::uuid,
    'c3d4e5f6-a7b8-9012-3456-789012cdefab'::uuid,
    CURRENT_DATE + INTERVAL '1 day', '10:00:00', 'cleaning', 'scheduled',
    'Regular cleaning and checkup'
),
(
    'f6a7b8c9-d0e1-2345-6789-012345fabcde'::uuid,
    'b2c3d4e5-f6a7-8901-2345-678901bcdefb'::uuid,
    'd4e5f6a7-b8c9-0123-4567-890123defabc'::uuid,
    CURRENT_DATE + INTERVAL '3 days', '14:30:00', 'consultation', 'scheduled',
    'Consultation for wisdom tooth removal'
) ON CONFLICT DO NOTHING;

-- Insert demo materials
INSERT INTO materials (
    id, doctor_id, name, category, brand, unit, cost_per_unit, current_stock, minimum_stock
) VALUES
(
    'a7b8c9d0-e1f2-3456-7890-123456abcdef'::uuid,
    'b2c3d4e5-f6a7-8901-2345-678901bcdefb'::uuid,
    'Dental Composite', 'Restorative', 'DentBrand', 'tube', 25.50, 15, 5
),
(
    'b8c9d0e1-f2a3-4567-8901-23456789abcd'::uuid,
    'b2c3d4e5-f6a7-8901-2345-678901bcdefb'::uuid,
    'Local Anesthetic', 'Anesthesia', 'MedSupply', 'vial', 12.75, 8, 3
) ON CONFLICT DO NOTHING;

SELECT 'COMPLETE DentoPro SaaS Database Setup with ALL tables completed successfully!' as status;
