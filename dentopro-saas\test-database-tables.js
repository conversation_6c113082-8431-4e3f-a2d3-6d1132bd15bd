// Test if database tables exist
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

const expectedTables = [
  'platform_admins',
  'doctors', 
  'subscription_plans',
  'subscriptions',
  'clinic_settings',
  'patients',
  'patient_verification',
  'appointments',
  'treatments',
  'prescriptions',
  'invoices',
  'materials',
  'stock_logs',
  'notifications',
  'file_uploads',
  'license_tracking',
  'audit_logs',
  'platform_analytics',
  'licenses'
];

async function testTables() {
  console.log('🔍 Testing database tables...\n');
  
  let foundTables = [];
  let missingTables = [];
  
  for (const tableName of expectedTables) {
    try {
      // Try to query the table (limit 0 to just check if it exists)
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(0);
      
      if (error) {
        console.log(`❌ ${tableName} - ${error.message}`);
        missingTables.push(tableName);
      } else {
        console.log(`✅ ${tableName} - EXISTS`);
        foundTables.push(tableName);
      }
    } catch (err) {
      console.log(`❌ ${tableName} - ${err.message}`);
      missingTables.push(tableName);
    }
  }
  
  console.log(`\n📊 SUMMARY:`);
  console.log(`✅ Found: ${foundTables.length}/${expectedTables.length} tables`);
  console.log(`❌ Missing: ${missingTables.length} tables`);
  
  if (missingTables.length > 0) {
    console.log(`\n🚨 Missing tables:`);
    missingTables.forEach(table => console.log(`   - ${table}`));
  }
  
  if (foundTables.length === expectedTables.length) {
    console.log(`\n🎉 ALL TABLES EXIST! Database setup is complete.`);
    
    // Test demo data
    console.log(`\n🔍 Testing demo data...`);
    
    try {
      const { data: admins } = await supabase.from('platform_admins').select('email').limit(1);
      const { data: doctors } = await supabase.from('doctors').select('email').limit(1);
      const { data: plans } = await supabase.from('subscription_plans').select('name').limit(1);
      
      console.log(`✅ Demo admin: ${admins?.[0]?.email || 'None'}`);
      console.log(`✅ Demo doctor: ${doctors?.[0]?.email || 'None'}`);
      console.log(`✅ Demo plan: ${plans?.[0]?.name || 'None'}`);
    } catch (err) {
      console.log(`⚠️ Demo data test failed: ${err.message}`);
    }
  } else {
    console.log(`\n🔧 Run the setup-complete-final.sql script in Supabase to create missing tables.`);
  }
}

testTables();
