// Test Supabase connection
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

console.log('Testing Supabase connection...');
console.log('URL:', supabaseUrl);
console.log('Key exists:', !!supabaseKey);

const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  try {
    // Test basic connection by trying to create a simple table
    const { data, error } = await supabase
      .rpc('exec_sql', {
        sql: 'SELECT 1 as test_connection'
      });

    if (error) {
      console.error('❌ Connection failed:', error.message);
      console.log('🔧 Trying alternative connection test...');

      // Alternative test - try to access auth users
      const { data: authData, error: authError } = await supabase.auth.getSession();

      if (authError) {
        console.error('❌ Auth connection also failed:', authError.message);
        return false;
      } else {
        console.log('✅ Connection successful via auth!');
        return true;
      }
    }

    console.log('✅ Connection successful!');
    console.log('📋 Test result:', data);
    return true;
  } catch (err) {
    console.error('❌ Connection error:', err.message);
    return false;
  }
}

testConnection();
