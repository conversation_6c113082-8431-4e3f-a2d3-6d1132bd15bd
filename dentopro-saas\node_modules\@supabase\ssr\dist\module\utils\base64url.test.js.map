{"version": 3, "file": "base64url.test.js", "sourceRoot": "", "sources": ["../../../src/utils/base64url.test.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AAChD,OAAO,EACL,iBAAiB,EACjB,mBAAmB,EACnB,cAAc,EACd,eAAe,GAChB,MAAM,aAAa,CAAC;AAErB,MAAM,QAAQ,GAAG;IACf,GAAG;IACH,IAAI;IACJ,KAAK;IACL,MAAM;IACN,aAAa;IACb,mBAAmB;IACnB,6BAA6B;IAC7B,sFAAsF;CACvF,CAAC;AAEF,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;IACjC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC3B,IAAI,CAAC,WAAW,OAAO,GAAG,EAAE,GAAG,EAAE;YAC/B,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CACxC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAC3C,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;IACnC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC3B,IAAI,CAAC,WAAW,OAAO,GAAG,EAAE,GAAG,EAAE;YAC/B,MAAM,CACJ,mBAAmB,CACjB,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CACvD,CACF,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,0CAA0C,EAAE,GAAG,EAAE;QACpD,MAAM,CAAC,GAAG,EAAE;YACV,mBAAmB,CAAC,GAAG,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC,CAAC;IAC1E,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,MAAM,QAAQ,GAAG;IACf,CAAC,IAAI,CAAC,EAAE,WAAW;IACnB,CAAC,IAAI,CAAC,EAAE,WAAW;IACnB,CAAC,IAAI,CAAC,EAAE,WAAW;IACnB,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,oBAAoB;IAC/B,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,oBAAoB;IAC/B,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,oBAAoB;CAChC,CAAC;AAEF,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;IAC9B,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC3B,IAAI,CAAC,uCAAuC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE;YAC/F,MAAM,CAAC,GAAG,EAAE;gBACV,MAAM,KAAK,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;gBAC3C,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;oBACvB,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;IAC/B,IAAI,CAAC,mCAAmC,EAAE,GAAG,EAAE;QAC7C,MAAM,gBAAgB,GAAG,QAAQ,GAAG,CAAC,CAAC;QACtC,MAAM,CAAC,GAAG,EAAE;YACV,eAAe,CAAC,gBAAgB,EAAE,GAAG,EAAE;gBACrC,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,OAAO,CACR,IAAI,KAAK,CACP,mCAAmC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CACnE,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}