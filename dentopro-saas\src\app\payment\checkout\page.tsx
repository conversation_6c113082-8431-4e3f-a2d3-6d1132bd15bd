'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Elements } from '@stripe/react-stripe-js';
import { getStripe } from '@/lib/stripe';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

// Payment form component (simplified for demo)
function PaymentForm({ clientSecret }: { clientSecret: string }) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // In a real implementation, you would use Stripe Elements here
      // For demo purposes, we'll simulate a successful payment
      setTimeout(() => {
        setSuccess(true);
        setLoading(false);
        
        // Redirect to dashboard after successful payment
        setTimeout(() => {
          router.push('/doctor/dashboard?payment=success');
        }, 2000);
      }, 2000);

    } catch (err: any) {
      setError(err.message || 'Payment failed');
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="text-center">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Payment Successful!</h3>
        <p className="text-gray-600 mb-4">
          Your subscription has been activated. Redirecting to dashboard...
        </p>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* Demo payment form */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2">💳 Demo Payment</h4>
        <p className="text-sm text-blue-800 mb-4">
          This is a demo environment. In production, this would be a real Stripe payment form.
        </p>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Card Number (Demo)
            </label>
            <input
              type="text"
              value="4242 4242 4242 4242"
              readOnly
              className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Expiry
              </label>
              <input
                type="text"
                value="12/25"
                readOnly
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                CVC
              </label>
              <input
                type="text"
                value="123"
                readOnly
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
              />
            </div>
          </div>
        </div>
      </div>

      <Button
        type="submit"
        disabled={loading}
        className="w-full"
        size="lg"
      >
        {loading ? 'Processing Payment...' : 'Complete Payment & Start Trial'}
      </Button>

      <div className="text-center text-sm text-gray-500">
        <p>🔒 Secure payment powered by Stripe</p>
        <p>Your trial starts immediately after payment</p>
      </div>
    </form>
  );
}

export default function CheckoutPage() {
  const [clientSecret, setClientSecret] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
  const searchParams = useSearchParams();
  const subscriptionId = searchParams.get('subscription_id');
  const clientSecretParam = searchParams.get('client_secret');

  useEffect(() => {
    if (clientSecretParam) {
      setClientSecret(clientSecretParam);
      setLoading(false);
    } else {
      setError('Invalid payment session');
      setLoading(false);
    }
  }, [clientSecretParam]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Setting up payment...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Payment Error</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <Link href="/auth/doctor-registration">
            <Button>Back to Registration</Button>
          </Link>
        </div>
      </div>
    );
  }

  const stripePromise = getStripe();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Complete Your Payment
          </h2>
          <p className="text-gray-600">
            Secure your subscription and start your 7-day free trial
          </p>
        </div>

        <div className="bg-white p-8 rounded-lg shadow-md">
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Subscription Summary</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex justify-between items-center mb-2">
                <span className="text-gray-600">Plan</span>
                <span className="font-medium">Premium Plan</span>
              </div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-gray-600">Price</span>
                <span className="font-medium">$15.00/month</span>
              </div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-gray-600">Trial Period</span>
                <span className="font-medium text-green-600">7 days free</span>
              </div>
              <hr className="my-3" />
              <div className="flex justify-between items-center">
                <span className="text-gray-900 font-semibold">Due Today</span>
                <span className="text-xl font-bold text-gray-900">$0.00</span>
              </div>
              <p className="text-sm text-gray-500 mt-2">
                You'll be charged $15.00 after your 7-day trial ends
              </p>
            </div>
          </div>

          {clientSecret && (
            <Elements stripe={stripePromise}>
              <PaymentForm clientSecret={clientSecret} />
            </Elements>
          )}
        </div>

        <div className="mt-6 text-center">
          <Link href="/auth/doctor-registration" className="text-sm text-gray-500 hover:text-gray-700">
            ← Back to registration
          </Link>
        </div>
      </div>
    </div>
  );
}
