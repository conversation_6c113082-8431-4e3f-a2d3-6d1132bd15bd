// Test environment configuration
require('dotenv').config({ path: '.env.local' });

console.log('🔧 Testing Environment Configuration...\n');

// Test 1: Required Environment Variables
console.log('📋 Test 1: Required Variables');

const requiredVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY',
  'NEXTAUTH_SECRET',
  'NEXTAUTH_URL'
];

requiredVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    console.log(`✅ ${varName}: ${value.substring(0, 20)}...`);
  } else {
    console.log(`❌ ${varName}: Missing`);
  }
});

// Test 2: OAuth Configuration
console.log('\n📋 Test 2: OAuth Configuration');

const oauthVars = [
  'GOOGLE_CLIENT_ID',
  'GOOGLE_CLIENT_SECRET'
];

oauthVars.forEach(varName => {
  const value = process.env[varName];
  if (value && value !== 'your-google-client-id-here' && value !== 'your-google-client-secret-here') {
    console.log(`✅ ${varName}: Configured`);
  } else {
    console.log(`⚠️ ${varName}: Needs real credentials`);
  }
});

// Test 3: 2FA Configuration
console.log('\n📋 Test 3: 2FA Configuration');

const twoFAVars = [
  'TWILIO_ACCOUNT_SID',
  'TWILIO_AUTH_TOKEN',
  'TWILIO_PHONE_NUMBER'
];

twoFAVars.forEach(varName => {
  const value = process.env[varName];
  if (value && !value.includes('your-twilio')) {
    console.log(`✅ ${varName}: Configured`);
  } else {
    console.log(`⚠️ ${varName}: Needs real credentials`);
  }
});

// Test 4: URL Validation
console.log('\n📋 Test 4: URL Validation');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
if (supabaseUrl) {
  try {
    const url = new URL(supabaseUrl);
    if (url.hostname.includes('supabase.co')) {
      console.log('✅ Supabase URL format valid');
      console.log(`  - Host: ${url.hostname}`);
    } else {
      console.log('⚠️ Supabase URL format unusual');
    }
  } catch (err) {
    console.log('❌ Supabase URL invalid format');
  }
} else {
  console.log('❌ Supabase URL missing');
}

const nextAuthUrl = process.env.NEXTAUTH_URL;
if (nextAuthUrl) {
  try {
    const url = new URL(nextAuthUrl);
    console.log('✅ NextAuth URL format valid');
    console.log(`  - URL: ${url.href}`);
  } catch (err) {
    console.log('❌ NextAuth URL invalid format');
  }
} else {
  console.log('❌ NextAuth URL missing');
}

// Test 5: Security Check
console.log('\n📋 Test 5: Security Check');

const nextAuthSecret = process.env.NEXTAUTH_SECRET;
if (nextAuthSecret && nextAuthSecret !== 'your-nextauth-secret-here-change-in-production') {
  console.log('✅ NextAuth secret is customized');
} else {
  console.log('⚠️ NextAuth secret needs to be changed for production');
}

console.log('\n🎯 Environment Configuration Test Complete!');
console.log('\n📋 Summary:');
console.log('✅ Basic configuration working');
console.log('⚠️ OAuth credentials need real values for testing');
console.log('⚠️ 2FA credentials need real values for testing');
console.log('⚠️ Change NextAuth secret for production');

console.log('\n📋 Next Steps for Full Testing:');
console.log('1. Get Google OAuth credentials from Google Cloud Console');
console.log('2. Get Twilio credentials for SMS testing');
console.log('3. Update environment variables with real values');
console.log('4. Test OAuth login flow');
console.log('5. Test 2FA SMS functionality');
