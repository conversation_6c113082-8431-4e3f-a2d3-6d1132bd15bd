"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6cad46c61b8b\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcRkFXWllcXE5ldyBmb2xkZXIgKDMpXFxkZW50b3Byby1zYWFzXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2Y2FkNDZjNjFiOGJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useAuth.tsx":
/*!*******************************!*\
  !*** ./src/hooks/useAuth.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [doctorId, setDoctorId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [patientId, setPatientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [is2FAEnabled, setIs2FAEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.createClientSupabaseClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Get initial session\n            const getInitialSession = {\n                \"AuthProvider.useEffect.getInitialSession\": async ()=>{\n                    const { data: { session }, error } = await supabase.auth.getSession();\n                    if (error) {\n                        console.error('Error getting session:', error);\n                    } else {\n                        await handleAuthStateChange(session);\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.getInitialSession\"];\n            getInitialSession();\n            // Listen for auth state changes\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    var _session_user;\n                    console.log('Auth state changed:', event, session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.email);\n                    await handleAuthStateChange(session);\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    subscription.unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        supabase.auth\n    ]);\n    const handleAuthStateChange = async (session)=>{\n        setSession(session);\n        if (session === null || session === void 0 ? void 0 : session.user) {\n            // Convert Supabase user to our User type\n            const userRole = await getUserRole(session.user);\n            const userWithRole = {\n                id: session.user.id,\n                email: session.user.email || '',\n                role: userRole,\n                createdAt: new Date(session.user.created_at),\n                updatedAt: new Date(session.user.updated_at || session.user.created_at)\n            };\n            setUser(userWithRole);\n            // Set role-specific IDs\n            if (userRole === _types__WEBPACK_IMPORTED_MODULE_2__.UserRole.DOCTOR) {\n                setDoctorId(session.user.id);\n                setPatientId(null);\n            } else if (userRole === _types__WEBPACK_IMPORTED_MODULE_2__.UserRole.PATIENT) {\n                setPatientId(session.user.id);\n                const doctorId = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.SupabaseUtils.getCurrentDoctorId();\n                setDoctorId(doctorId);\n            } else {\n                setDoctorId(null);\n                setPatientId(null);\n            }\n        } else {\n            setUser(null);\n            setDoctorId(null);\n            setPatientId(null);\n        }\n    };\n    const getUserRole = async (supabaseUser)=>{\n        var _supabaseUser_user_metadata, _supabaseUser_app_metadata;\n        // Check user metadata first\n        const metadataRole = ((_supabaseUser_user_metadata = supabaseUser.user_metadata) === null || _supabaseUser_user_metadata === void 0 ? void 0 : _supabaseUser_user_metadata.role) || ((_supabaseUser_app_metadata = supabaseUser.app_metadata) === null || _supabaseUser_app_metadata === void 0 ? void 0 : _supabaseUser_app_metadata.role);\n        if (metadataRole) {\n            return metadataRole;\n        }\n        // Check if user exists in platform_admins table\n        try {\n            const { data: admin } = await supabase.from('platform_admins').select('id').eq('id', supabaseUser.id).single();\n            if (admin) {\n                return _types__WEBPACK_IMPORTED_MODULE_2__.UserRole.SUPER_ADMIN;\n            }\n        } catch (error) {\n        // User not found in platform_admins, continue checking\n        }\n        // Check if user exists in doctors table\n        try {\n            const { data: doctor } = await supabase.from('doctors').select('id').eq('id', supabaseUser.id).single();\n            if (doctor) {\n                return _types__WEBPACK_IMPORTED_MODULE_2__.UserRole.DOCTOR;\n            }\n        } catch (error) {\n        // User not found in doctors, continue checking\n        }\n        // Check if user exists in patients table\n        try {\n            const { data: patient } = await supabase.from('patients').select('id').eq('id', supabaseUser.id).single();\n            if (patient) {\n                return _types__WEBPACK_IMPORTED_MODULE_2__.UserRole.PATIENT;\n            }\n        } catch (error) {\n        // User not found in patients\n        }\n        // Default to patient role for new users\n        return _types__WEBPACK_IMPORTED_MODULE_2__.UserRole.PATIENT;\n    };\n    const login = async (email, password)=>{\n        setLoading(true);\n        try {\n            const result = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.SupabaseUtils.signInWithPassword(email, password);\n            if (result.error) {\n                throw result.error;\n            }\n            return result;\n        } catch (error) {\n            console.error('Login failed:', error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loginWithGoogle = async ()=>{\n        setLoading(true);\n        try {\n            const result = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.SupabaseUtils.signInWithGoogle();\n            if (result.error) {\n                throw result.error;\n            }\n            return result;\n        } catch (error) {\n            console.error('Google login failed:', error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        setLoading(true);\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.SupabaseUtils.signOut();\n            if (error) {\n                throw error;\n            }\n        } catch (error) {\n            console.error('Logout failed:', error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const register = async (userData)=>{\n        setLoading(true);\n        try {\n            const result = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.SupabaseUtils.signUp(userData.email, userData.password, {\n                role: userData.role,\n                ...userData.metadata\n            });\n            if (result.error) {\n                throw result.error;\n            }\n            return result;\n        } catch (error) {\n            console.error('Registration failed:', error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const resetPassword = async (email)=>{\n        try {\n            const result = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.SupabaseUtils.resetPassword(email);\n            if (result.error) {\n                throw result.error;\n            }\n            return result;\n        } catch (error) {\n            console.error('Password reset failed:', error);\n            throw error;\n        }\n    };\n    const hasRole = (role)=>{\n        return (user === null || user === void 0 ? void 0 : user.role) === role;\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        login,\n        loginWithGoogle,\n        logout,\n        register,\n        resetPassword,\n        isAuthenticated: !!user,\n        hasRole,\n        isSuperAdmin: hasRole(_types__WEBPACK_IMPORTED_MODULE_2__.UserRole.SUPER_ADMIN),\n        isDoctor: hasRole(_types__WEBPACK_IMPORTED_MODULE_2__.UserRole.DOCTOR),\n        isPatient: hasRole(_types__WEBPACK_IMPORTED_MODULE_2__.UserRole.PATIENT),\n        doctorId,\n        patientId\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\hooks\\\\useAuth.tsx\",\n        lineNumber: 276,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"0sRJphQc92E9vokau8mqaOYcUs4=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAuth.tsx\n"));

/***/ })

});