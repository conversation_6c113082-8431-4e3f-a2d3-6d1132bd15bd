-- DentoPro SaaS Platform - Row Level Security Policies for Supabase
-- Migration: 20241221000002_rls_policies.sql
-- Description: Implement Row Level Security for multi-tenant data isolation

-- =============================================
-- ENABLE ROW LEVEL SECURITY
-- =============================================

-- Enable RLS on all doctor-specific tables
ALTER TABLE clinic_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE patients ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE treatments ENABLE ROW LEVEL SECURITY;
ALTER TABLE materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE material_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE prescriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE file_uploads ENABLE ROW LEVEL SECURITY;
ALTER TABLE platform_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE license_tracking ENABLE ROW LEVEL SECURITY;

-- Platform admin tables (no RLS needed as they're admin-only)
-- ALTER TABLE platform_admins ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE doctors ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- =============================================
-- HELPER FUNCTIONS FOR RLS
-- =============================================

-- Function to get current user's role from JWT
CREATE OR REPLACE FUNCTION auth.get_user_role()
RETURNS TEXT AS $$
BEGIN
    RETURN COALESCE(
        auth.jwt() ->> 'role',
        (auth.jwt() -> 'user_metadata' ->> 'role'),
        (auth.jwt() -> 'app_metadata' ->> 'role'),
        'patient'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current user's doctor_id
CREATE OR REPLACE FUNCTION auth.get_doctor_id()
RETURNS UUID AS $$
DECLARE
    user_role TEXT;
    user_id UUID;
    doctor_id UUID;
BEGIN
    user_role := auth.get_user_role();
    user_id := auth.uid();
    
    IF user_role = 'doctor' THEN
        RETURN user_id;
    ELSIF user_role = 'patient' THEN
        -- Get doctor_id from patients table
        SELECT p.doctor_id INTO doctor_id
        FROM patients p
        WHERE p.id = user_id;
        RETURN doctor_id;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is super admin
CREATE OR REPLACE FUNCTION auth.is_super_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN auth.get_user_role() = 'super_admin';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is doctor
CREATE OR REPLACE FUNCTION auth.is_doctor()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN auth.get_user_role() = 'doctor';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is patient
CREATE OR REPLACE FUNCTION auth.is_patient()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN auth.get_user_role() = 'patient';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- RLS POLICIES FOR CLINIC SETTINGS
-- =============================================

-- Super admins can access all clinic settings
CREATE POLICY "Super admins can access all clinic settings" ON clinic_settings
    FOR ALL TO authenticated
    USING (auth.is_super_admin());

-- Doctors can only access their own clinic settings
CREATE POLICY "Doctors can access their own clinic settings" ON clinic_settings
    FOR ALL TO authenticated
    USING (
        auth.is_doctor() AND 
        doctor_id = auth.uid()
    );

-- =============================================
-- RLS POLICIES FOR PATIENTS
-- =============================================

-- Super admins can access all patients
CREATE POLICY "Super admins can access all patients" ON patients
    FOR ALL TO authenticated
    USING (auth.is_super_admin());

-- Doctors can only access their own patients
CREATE POLICY "Doctors can access their own patients" ON patients
    FOR ALL TO authenticated
    USING (
        auth.is_doctor() AND 
        doctor_id = auth.uid()
    );

-- Patients can only view their own record
CREATE POLICY "Patients can view their own record" ON patients
    FOR SELECT TO authenticated
    USING (
        auth.is_patient() AND 
        id = auth.uid()
    );

-- Patients can update their own basic information
CREATE POLICY "Patients can update their own basic info" ON patients
    FOR UPDATE TO authenticated
    USING (
        auth.is_patient() AND 
        id = auth.uid()
    )
    WITH CHECK (
        auth.is_patient() AND 
        id = auth.uid()
    );

-- =============================================
-- RLS POLICIES FOR APPOINTMENTS
-- =============================================

-- Super admins can access all appointments
CREATE POLICY "Super admins can access all appointments" ON appointments
    FOR ALL TO authenticated
    USING (auth.is_super_admin());

-- Doctors can access their own appointments
CREATE POLICY "Doctors can access their own appointments" ON appointments
    FOR ALL TO authenticated
    USING (
        auth.is_doctor() AND 
        doctor_id = auth.uid()
    );

-- Patients can view their own appointments
CREATE POLICY "Patients can view their own appointments" ON appointments
    FOR SELECT TO authenticated
    USING (
        auth.is_patient() AND 
        patient_id = auth.uid()
    );

-- Patients can book new appointments (online booking)
CREATE POLICY "Patients can book appointments" ON appointments
    FOR INSERT TO authenticated
    WITH CHECK (
        auth.is_patient() AND 
        patient_id = auth.uid() AND
        source = 'patient_online'
    );

-- =============================================
-- RLS POLICIES FOR TREATMENTS
-- =============================================

-- Super admins can access all treatments
CREATE POLICY "Super admins can access all treatments" ON treatments
    FOR ALL TO authenticated
    USING (auth.is_super_admin());

-- Doctors can access their own treatments
CREATE POLICY "Doctors can access their own treatments" ON treatments
    FOR ALL TO authenticated
    USING (
        auth.is_doctor() AND 
        doctor_id = auth.uid()
    );

-- Patients can view their own treatments
CREATE POLICY "Patients can view their own treatments" ON treatments
    FOR SELECT TO authenticated
    USING (
        auth.is_patient() AND 
        patient_id = auth.uid()
    );

-- =============================================
-- RLS POLICIES FOR MATERIALS
-- =============================================

-- Super admins can access all materials
CREATE POLICY "Super admins can access all materials" ON materials
    FOR ALL TO authenticated
    USING (auth.is_super_admin());

-- Doctors can access their own materials
CREATE POLICY "Doctors can access their own materials" ON materials
    FOR ALL TO authenticated
    USING (
        auth.is_doctor() AND 
        doctor_id = auth.uid()
    );

-- =============================================
-- RLS POLICIES FOR MATERIAL LOGS
-- =============================================

-- Super admins can access all material logs
CREATE POLICY "Super admins can access all material logs" ON material_logs
    FOR ALL TO authenticated
    USING (auth.is_super_admin());

-- Doctors can access their own material logs
CREATE POLICY "Doctors can access their own material logs" ON material_logs
    FOR ALL TO authenticated
    USING (
        auth.is_doctor() AND 
        doctor_id = auth.uid()
    );

-- =============================================
-- RLS POLICIES FOR PRESCRIPTIONS
-- =============================================

-- Super admins can access all prescriptions
CREATE POLICY "Super admins can access all prescriptions" ON prescriptions
    FOR ALL TO authenticated
    USING (auth.is_super_admin());

-- Doctors can access their own prescriptions
CREATE POLICY "Doctors can access their own prescriptions" ON prescriptions
    FOR ALL TO authenticated
    USING (
        auth.is_doctor() AND 
        doctor_id = auth.uid()
    );

-- Patients can view their own prescriptions
CREATE POLICY "Patients can view their own prescriptions" ON prescriptions
    FOR SELECT TO authenticated
    USING (
        auth.is_patient() AND 
        patient_id = auth.uid()
    );

-- =============================================
-- RLS POLICIES FOR INVOICES
-- =============================================

-- Super admins can access all invoices
CREATE POLICY "Super admins can access all invoices" ON invoices
    FOR ALL TO authenticated
    USING (auth.is_super_admin());

-- Doctors can access their own invoices
CREATE POLICY "Doctors can access their own invoices" ON invoices
    FOR ALL TO authenticated
    USING (
        auth.is_doctor() AND 
        doctor_id = auth.uid()
    );

-- Patients can view their own invoices
CREATE POLICY "Patients can view their own invoices" ON invoices
    FOR SELECT TO authenticated
    USING (
        auth.is_patient() AND 
        patient_id = auth.uid()
    );

-- =============================================
-- RLS POLICIES FOR NOTIFICATIONS
-- =============================================

-- Super admins can access all notifications
CREATE POLICY "Super admins can access all notifications" ON notifications
    FOR ALL TO authenticated
    USING (auth.is_super_admin());

-- Users can access notifications sent to them
CREATE POLICY "Users can access their own notifications" ON notifications
    FOR SELECT TO authenticated
    USING (
        recipient_id = auth.uid() OR
        (auth.is_doctor() AND doctor_id = auth.uid())
    );

-- Users can mark their notifications as read
CREATE POLICY "Users can update their own notifications" ON notifications
    FOR UPDATE TO authenticated
    USING (
        recipient_id = auth.uid()
    )
    WITH CHECK (
        recipient_id = auth.uid()
    );

-- =============================================
-- RLS POLICIES FOR FILE UPLOADS
-- =============================================

-- Super admins can access all files
CREATE POLICY "Super admins can access all files" ON file_uploads
    FOR ALL TO authenticated
    USING (auth.is_super_admin());

-- Doctors can access their own files
CREATE POLICY "Doctors can access their own files" ON file_uploads
    FOR ALL TO authenticated
    USING (
        auth.is_doctor() AND 
        doctor_id = auth.uid()
    );

-- Patients can view their own files
CREATE POLICY "Patients can view their own files" ON file_uploads
    FOR SELECT TO authenticated
    USING (
        auth.is_patient() AND 
        patient_id = auth.uid()
    );

-- =============================================
-- RLS POLICIES FOR ANALYTICS & MONITORING
-- =============================================

-- Platform analytics - Super admins see all, doctors see their own
CREATE POLICY "Analytics access policy" ON platform_analytics
    FOR SELECT TO authenticated
    USING (
        auth.is_super_admin() OR
        (auth.is_doctor() AND 
         (doctor_id IS NULL OR doctor_id = auth.uid()))
    );

-- License tracking - Super admins see all, doctors see their own
CREATE POLICY "License tracking access policy" ON license_tracking
    FOR ALL TO authenticated
    USING (
        auth.is_super_admin() OR
        (auth.is_doctor() AND doctor_id = auth.uid())
    );

-- =============================================
-- SECURITY VIEWS FOR SAFE DATA ACCESS
-- =============================================

-- View for doctors to see their patient summary
CREATE VIEW doctor_patient_summary AS
SELECT 
    p.id,
    p.full_name,
    p.phone,
    p.email,
    p.date_of_birth,
    p.gender,
    COUNT(a.id) as total_appointments,
    MAX(a.appointment_date) as last_visit,
    COUNT(t.id) as total_treatments,
    SUM(i.total_amount) as total_billed
FROM patients p
LEFT JOIN appointments a ON p.id = a.patient_id
LEFT JOIN treatments t ON p.id = t.patient_id
LEFT JOIN invoices i ON p.id = i.patient_id
WHERE p.doctor_id = auth.get_doctor_id()
GROUP BY p.id, p.full_name, p.phone, p.email, p.date_of_birth, p.gender;

-- View for patients to see their own summary
CREATE VIEW patient_summary AS
SELECT 
    p.full_name,
    p.phone,
    p.email,
    p.date_of_birth,
    COUNT(a.id) as total_appointments,
    MAX(a.appointment_date) as last_visit,
    COUNT(t.id) as total_treatments,
    SUM(i.total_amount) as total_billed,
    SUM(CASE WHEN i.status = 'paid' THEN i.total_amount ELSE 0 END) as total_paid,
    SUM(CASE WHEN i.status != 'paid' THEN i.total_amount ELSE 0 END) as outstanding_balance
FROM patients p
LEFT JOIN appointments a ON p.id = a.patient_id
LEFT JOIN treatments t ON p.id = t.patient_id
LEFT JOIN invoices i ON p.id = i.patient_id
WHERE p.id = auth.uid()
GROUP BY p.full_name, p.phone, p.email, p.date_of_birth;

-- Enable RLS on views
ALTER VIEW doctor_patient_summary SET (security_barrier = true);
ALTER VIEW patient_summary SET (security_barrier = true);
