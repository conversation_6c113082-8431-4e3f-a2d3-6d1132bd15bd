(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},7740:(e,t,s)=>{"use strict";s.d(t,{ThemeProvider:()=>o});var r=s(5155),l=s(2115);let n=(0,l.createContext)({theme:"system",setTheme:()=>null});function o(e){let{children:t,defaultTheme:s="system",storageKey:o="dentopro-ui-theme",...a}=e,[i,d]=(0,l.useState)(()=>{var e;return(null==(e=localStorage)?void 0:e.getItem(o))||s});return(0,l.useEffect)(()=>{let e=window.document.documentElement;if(e.classList.remove("light","dark"),"system"===i){let t=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";e.classList.add(t);return}e.classList.add(i)},[i]),(0,r.jsx)(n.Provider,{...a,value:{theme:i,setTheme:e=>{var t;null==(t=localStorage)||t.setItem(o,e),d(e)}},children:t})}},9410:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,347,23)),Promise.resolve().then(s.bind(s,7740)),Promise.resolve().then(s.bind(s,1866))}},e=>{e.O(0,[690,535,866,441,964,358],()=>e(e.s=9410)),_N_E=e.O()}]);