// Database testing framework for DentoPro SaaS platform
import { Pool, PoolClient } from 'pg';
import { db, transaction } from './database';

// Test database configuration
interface TestConfig {
  testDbName: string;
  cleanupAfterTests: boolean;
  seedData: boolean;
  isolateTests: boolean;
}

// Test data generators
export class TestDataGenerator {
  static generateDoctor(overrides: Partial<any> = {}): any {
    return {
      email: `doctor${Date.now()}@test.com`,
      password_hash: '$2b$12$test.hash.value',
      full_name: 'Dr. Test Doctor',
      license_number: `LIC${Date.now()}`,
      clinic_name: 'Test Dental Clinic',
      phone: '+**********',
      subscription_plan: 'premium',
      subscription_status: 'active',
      is_active: true,
      ...overrides
    };
  }
  
  static generatePatient(doctorId: string, overrides: Partial<any> = {}): any {
    return {
      doctor_id: doctorId,
      email: `patient${Date.now()}@test.com`,
      full_name: 'Test Patient',
      phone: `+123456${Math.floor(Math.random() * 10000)}`,
      date_of_birth: '1990-01-01',
      gender: 'male',
      address: '123 Test Street, Test City',
      is_verified: true,
      ...overrides
    };
  }
  
  static generateAppointment(doctorId: string, patientId: string, overrides: Partial<any> = {}): any {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    return {
      doctor_id: doctorId,
      patient_id: patientId,
      appointment_date: tomorrow.toISOString().split('T')[0],
      appointment_time: '10:00',
      duration: 30,
      appointment_type: 'checkup',
      status: 'scheduled',
      source: 'doctor',
      ...overrides
    };
  }
  
  static generateTreatment(doctorId: string, patientId: string, appointmentId?: string, overrides: Partial<any> = {}): any {
    return {
      doctor_id: doctorId,
      patient_id: patientId,
      appointment_id: appointmentId,
      procedure_name: 'Dental Cleaning',
      tooth_number: '14',
      diagnosis: 'Routine cleaning',
      notes: 'Patient responded well to treatment',
      cost: 150.00,
      materials_used: JSON.stringify([{ material_id: 'test-material', quantity: 1 }]),
      ...overrides
    };
  }
  
  static generateMaterial(doctorId: string, overrides: Partial<any> = {}): any {
    return {
      doctor_id: doctorId,
      name: 'Test Dental Material',
      category: 'cleaning',
      unit: 'ml',
      current_stock: 100,
      low_threshold: 10,
      cost_per_unit: 5.00,
      supplier: 'Test Supplier',
      is_active: true,
      ...overrides
    };
  }
}

// Database test utilities
export class DatabaseTestUtils {
  private static testConfig: TestConfig = {
    testDbName: 'dentopro_test',
    cleanupAfterTests: true,
    seedData: false,
    isolateTests: true
  };
  
  // Setup test database
  static async setupTestDatabase(): Promise<void> {
    // Create test database if it doesn't exist
    const adminDb = new Pool({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      database: 'postgres',
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || ''
    });
    
    try {
      await adminDb.query(`CREATE DATABASE ${this.testConfig.testDbName}`);
    } catch (error) {
      // Database might already exist
      console.log('Test database already exists or creation failed');
    } finally {
      await adminDb.end();
    }
    
    // Run migrations on test database
    await this.runTestMigrations();
  }
  
  // Run migrations on test database
  static async runTestMigrations(): Promise<void> {
    const fs = require('fs');
    const path = require('path');
    
    const migrationFiles = [
      '001_initial_schema.sql',
      '002_indexes_and_constraints.sql',
      '003_row_level_security.sql'
    ];
    
    for (const file of migrationFiles) {
      const migrationPath = path.join(process.cwd(), 'database', 'migrations', file);
      if (fs.existsSync(migrationPath)) {
        const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
        await db.query(migrationSQL);
      }
    }
  }
  
  // Clean test database
  static async cleanTestDatabase(): Promise<void> {
    const tables = [
      'file_uploads', 'notifications', 'invoices', 'prescriptions',
      'material_logs', 'materials', 'treatments', 'appointments',
      'patients', 'clinic_settings', 'license_tracking',
      'platform_analytics', 'subscriptions', 'doctors', 'platform_admins'
    ];
    
    for (const table of tables) {
      await db.query(`TRUNCATE TABLE ${table} CASCADE`);
    }
  }
  
  // Create test doctor with full setup
  static async createTestDoctor(): Promise<any> {
    const doctorData = TestDataGenerator.generateDoctor();
    const doctor = await db.insert('doctors', doctorData);
    
    // Create clinic settings
    const clinicSettings = {
      doctor_id: doctor.id,
      timezone: 'UTC',
      language: 'en',
      currency: 'USD',
      working_hours: JSON.stringify({
        monday: { isOpen: true, startTime: '09:00', endTime: '17:00' },
        tuesday: { isOpen: true, startTime: '09:00', endTime: '17:00' },
        wednesday: { isOpen: true, startTime: '09:00', endTime: '17:00' },
        thursday: { isOpen: true, startTime: '09:00', endTime: '17:00' },
        friday: { isOpen: true, startTime: '09:00', endTime: '17:00' },
        saturday: { isOpen: false, startTime: '', endTime: '' },
        sunday: { isOpen: false, startTime: '', endTime: '' }
      }),
      appointment_duration: 30
    };
    
    await db.insert('clinic_settings', clinicSettings);
    
    return doctor;
  }
  
  // Create test patient for a doctor
  static async createTestPatient(doctorId: string): Promise<any> {
    const patientData = TestDataGenerator.generatePatient(doctorId);
    return await db.insert('patients', patientData);
  }
  
  // Create complete test scenario
  static async createTestScenario(): Promise<{
    doctor: any;
    patients: any[];
    appointments: any[];
    treatments: any[];
    materials: any[];
  }> {
    const doctor = await this.createTestDoctor();
    
    // Create patients
    const patients = [];
    for (let i = 0; i < 3; i++) {
      const patient = await this.createTestPatient(doctor.id);
      patients.push(patient);
    }
    
    // Create appointments
    const appointments = [];
    for (const patient of patients) {
      const appointmentData = TestDataGenerator.generateAppointment(doctor.id, patient.id);
      const appointment = await db.insert('appointments', appointmentData);
      appointments.push(appointment);
    }
    
    // Create treatments
    const treatments = [];
    for (let i = 0; i < appointments.length; i++) {
      const treatmentData = TestDataGenerator.generateTreatment(
        doctor.id,
        patients[i].id,
        appointments[i].id
      );
      const treatment = await db.insert('treatments', treatmentData);
      treatments.push(treatment);
    }
    
    // Create materials
    const materials = [];
    for (let i = 0; i < 5; i++) {
      const materialData = TestDataGenerator.generateMaterial(doctor.id);
      const material = await db.insert('materials', materialData);
      materials.push(material);
    }
    
    return { doctor, patients, appointments, treatments, materials };
  }
}

// Test suite for database operations
export class DatabaseTestSuite {
  // Test data isolation between doctors
  static async testDataIsolation(): Promise<boolean> {
    try {
      // Create two doctors
      const doctor1 = await DatabaseTestUtils.createTestDoctor();
      const doctor2 = await DatabaseTestUtils.createTestDoctor();
      
      // Create patients for each doctor
      const patient1 = await DatabaseTestUtils.createTestPatient(doctor1.id);
      const patient2 = await DatabaseTestUtils.createTestPatient(doctor2.id);
      
      // Test that doctor1 cannot see doctor2's patients
      const doctor1Patients = await db.findMany('patients', { doctor_id: doctor1.id });
      const doctor2Patients = await db.findMany('patients', { doctor_id: doctor2.id });
      
      // Verify isolation
      const hasDoctor1OnlyOwnPatients = doctor1Patients.every(p => p.doctor_id === doctor1.id);
      const hasDoctor2OnlyOwnPatients = doctor2Patients.every(p => p.doctor_id === doctor2.id);
      const noDataLeakage = !doctor1Patients.some(p => p.doctor_id === doctor2.id) &&
                           !doctor2Patients.some(p => p.doctor_id === doctor1.id);
      
      return hasDoctor1OnlyOwnPatients && hasDoctor2OnlyOwnPatients && noDataLeakage;
    } catch (error) {
      console.error('Data isolation test failed:', error);
      return false;
    }
  }
  
  // Test referential integrity
  static async testReferentialIntegrity(): Promise<boolean> {
    try {
      const { doctor, patients, appointments } = await DatabaseTestUtils.createTestScenario();
      
      // Test cascade delete - deleting doctor should delete all related data
      await db.delete('doctors', { id: doctor.id });
      
      // Verify all related data is deleted
      const remainingPatients = await db.findMany('patients', { doctor_id: doctor.id });
      const remainingAppointments = await db.findMany('appointments', { doctor_id: doctor.id });
      
      return remainingPatients.length === 0 && remainingAppointments.length === 0;
    } catch (error) {
      console.error('Referential integrity test failed:', error);
      return false;
    }
  }
  
  // Test performance with large datasets
  static async testPerformance(): Promise<{ queryTime: number; insertTime: number; updateTime: number }> {
    const doctor = await DatabaseTestUtils.createTestDoctor();
    
    // Test insert performance
    const insertStart = Date.now();
    const insertPromises = [];
    for (let i = 0; i < 100; i++) {
      insertPromises.push(DatabaseTestUtils.createTestPatient(doctor.id));
    }
    await Promise.all(insertPromises);
    const insertTime = Date.now() - insertStart;
    
    // Test query performance
    const queryStart = Date.now();
    await db.findMany('patients', { doctor_id: doctor.id });
    const queryTime = Date.now() - queryStart;
    
    // Test update performance
    const updateStart = Date.now();
    await db.query('UPDATE patients SET notes = $1 WHERE doctor_id = $2', ['Updated notes', doctor.id]);
    const updateTime = Date.now() - updateStart;
    
    return { queryTime, insertTime, updateTime };
  }
  
  // Test transaction rollback
  static async testTransactionRollback(): Promise<boolean> {
    try {
      const doctor = await DatabaseTestUtils.createTestDoctor();
      const initialPatientCount = await db.count('patients', { doctor_id: doctor.id });
      
      // Attempt transaction that should fail
      try {
        await transaction(async (client) => {
          // Insert a patient
          await client.query(
            'INSERT INTO patients (doctor_id, full_name, phone) VALUES ($1, $2, $3)',
            [doctor.id, 'Test Patient', '+**********']
          );
          
          // This should fail due to constraint violation
          await client.query(
            'INSERT INTO patients (doctor_id, full_name, phone) VALUES ($1, $2, $3)',
            [doctor.id, 'Another Patient', '+**********'] // Same phone number
          );
        });
      } catch (error) {
        // Expected to fail
      }
      
      // Verify rollback - patient count should be unchanged
      const finalPatientCount = await db.count('patients', { doctor_id: doctor.id });
      return finalPatientCount === initialPatientCount;
    } catch (error) {
      console.error('Transaction rollback test failed:', error);
      return false;
    }
  }
  
  // Run all tests
  static async runAllTests(): Promise<any> {
    console.log('Starting database tests...');
    
    // Setup test environment
    await DatabaseTestUtils.setupTestDatabase();
    
    const results = {
      dataIsolation: false,
      referentialIntegrity: false,
      transactionRollback: false,
      performance: { queryTime: 0, insertTime: 0, updateTime: 0 }
    };
    
    try {
      // Clean database before tests
      await DatabaseTestUtils.cleanTestDatabase();
      
      // Run tests
      results.dataIsolation = await this.testDataIsolation();
      console.log('✓ Data isolation test:', results.dataIsolation ? 'PASSED' : 'FAILED');
      
      await DatabaseTestUtils.cleanTestDatabase();
      results.referentialIntegrity = await this.testReferentialIntegrity();
      console.log('✓ Referential integrity test:', results.referentialIntegrity ? 'PASSED' : 'FAILED');
      
      await DatabaseTestUtils.cleanTestDatabase();
      results.transactionRollback = await this.testTransactionRollback();
      console.log('✓ Transaction rollback test:', results.transactionRollback ? 'PASSED' : 'FAILED');
      
      await DatabaseTestUtils.cleanTestDatabase();
      results.performance = await this.testPerformance();
      console.log('✓ Performance test completed:', results.performance);
      
    } finally {
      // Cleanup
      if (DatabaseTestUtils['testConfig'].cleanupAfterTests) {
        await DatabaseTestUtils.cleanTestDatabase();
      }
    }
    
    return results;
  }
}

// Integration test helpers
export class IntegrationTestHelpers {
  // Test complete patient workflow
  static async testPatientWorkflow(): Promise<boolean> {
    try {
      const doctor = await DatabaseTestUtils.createTestDoctor();
      
      // 1. Register patient
      const patient = await DatabaseTestUtils.createTestPatient(doctor.id);
      
      // 2. Book appointment
      const appointmentData = TestDataGenerator.generateAppointment(doctor.id, patient.id);
      const appointment = await db.insert('appointments', appointmentData);
      
      // 3. Record treatment
      const treatmentData = TestDataGenerator.generateTreatment(doctor.id, patient.id, appointment.id);
      const treatment = await db.insert('treatments', treatmentData);
      
      // 4. Create prescription
      const prescriptionData = {
        doctor_id: doctor.id,
        patient_id: patient.id,
        appointment_id: appointment.id,
        prescription_number: `RX${Date.now()}`,
        medications: JSON.stringify([{ name: 'Ibuprofen', dosage: '400mg', frequency: 'twice daily' }]),
        qr_code: 'test-qr-code',
        valid_until: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
      };
      const prescription = await db.insert('prescriptions', prescriptionData);
      
      // 5. Generate invoice
      const invoiceData = {
        doctor_id: doctor.id,
        patient_id: patient.id,
        appointment_id: appointment.id,
        invoice_number: `INV${Date.now()}`,
        items: JSON.stringify([{ description: 'Dental Cleaning', quantity: 1, unitPrice: 150, total: 150 }]),
        subtotal: 150,
        total_amount: 150,
        status: 'sent'
      };
      const invoice = await db.insert('invoices', invoiceData);
      
      // Verify all records were created and linked correctly
      const verifyPatient = await db.findOne('patients', { id: patient.id });
      const verifyAppointment = await db.findOne('appointments', { id: appointment.id });
      const verifyTreatment = await db.findOne('treatments', { id: treatment.id });
      const verifyPrescription = await db.findOne('prescriptions', { id: prescription.id });
      const verifyInvoice = await db.findOne('invoices', { id: invoice.id });
      
      return verifyPatient && verifyAppointment && verifyTreatment && 
             verifyPrescription && verifyInvoice &&
             verifyAppointment.patient_id === patient.id &&
             verifyTreatment.appointment_id === appointment.id &&
             verifyPrescription.appointment_id === appointment.id &&
             verifyInvoice.appointment_id === appointment.id;
    } catch (error) {
      console.error('Patient workflow test failed:', error);
      return false;
    }
  }
}

export default {
  TestDataGenerator,
  DatabaseTestUtils,
  DatabaseTestSuite,
  IntegrationTestHelpers
};
