"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[866],{1866:(e,t,r)=>{r.d(t,{AuthProvider:()=>y,A:()=>p});var a=r(5155),i=r(2115),s=r(3717),n=r(5647),o=r(9535),l=r(9509);let u=l.env.NEXT_PUBLIC_SUPABASE_URL,c=l.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,d=l.env.SUPABASE_SERVICE_ROLE_KEY;if(!u||!c)throw Error("Missing Supabase environment variables. Please check your .env file.");(0,n.UU)(u,c,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0}}),(0,n.UU)(u,d||c,{auth:{autoRefreshToken:!1,persistSession:!1}});let h=()=>(0,o.createBrowserClient)(u,c);class w{static async getCurrentUser(){var e,t;let r=h(),{data:{user:a},error:i}=await r.auth.getUser();if(i||!a)return null;let s=(null==(e=a.user_metadata)?void 0:e.role)||(null==(t=a.app_metadata)?void 0:t.role)||"patient";return{...a,role:s}}static async hasRole(e){let t=await this.getCurrentUser();return(null==t?void 0:t.role)===e}static async getCurrentDoctorId(){let e=await this.getCurrentUser();if((null==e?void 0:e.role)==="doctor")return e.id;if((null==e?void 0:e.role)==="patient"){let t=h(),{data:r}=await t.from("patients").select("doctor_id").eq("id",e.id).single();return(null==r?void 0:r.doctor_id)||null}return null}static async getCurrentPatientId(){let e=await this.getCurrentUser();return(null==e?void 0:e.role)==="patient"?e.id:null}static async signInWithPassword(e,t){let r=h();return await r.auth.signInWithPassword({email:e,password:t})}static async signInWithGoogle(){let e=h();return await e.auth.signInWithOAuth({provider:"google",options:{redirectTo:"".concat(window.location.origin,"/auth/callback")}})}static async signUp(e,t,r){let a=h();return await a.auth.signUp({email:e,password:t,options:{data:r}})}static async signOut(){let e=h();return await e.auth.signOut()}static async updateUserMetadata(e){let t=h();return await t.auth.updateUser({data:e})}static async resetPassword(e){let t=h();return await t.auth.resetPasswordForEmail(e,{redirectTo:"".concat(window.location.origin,"/auth/reset-password")})}}class f{async findMany(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=this.supabase.from(e).select(r.select||"*");if(Object.entries(t).forEach(e=>{let[t,r]=e;a=a.eq(t,r)}),r.orderBy){let[e,t]=r.orderBy.split(" ");a=a.order(e,{ascending:"desc"!==t})}r.limit&&(a=a.limit(r.limit)),r.offset&&(a=a.range(r.offset,r.offset+(r.limit||10)-1));let{data:i,error:s}=await a;if(s)throw console.error("Database query error:",s),s;return i}async findOne(e,t){let r=this.supabase.from(e).select("*");Object.entries(t).forEach(e=>{let[t,a]=e;r=r.eq(t,a)});let{data:a,error:i}=await r.single();if(i&&"PGRST116"!==i.code)throw console.error("Database query error:",i),i;return a}async insert(e,t){let{data:r,error:a}=await this.supabase.from(e).insert(t).select().single();if(a)throw console.error("Database insert error:",a),a;return r}async update(e,t,r){let a=this.supabase.from(e).update(t);Object.entries(r).forEach(e=>{let[t,r]=e;a=a.eq(t,r)});let{data:i,error:s}=await a.select().single();if(s&&"PGRST116"!==s.code)throw console.error("Database update error:",s),s;return i}async delete(e,t){let r=this.supabase.from(e).delete();Object.entries(t).forEach(e=>{let[t,a]=e;r=r.eq(t,a)});let{error:a}=await r;if(a)throw console.error("Database delete error:",a),a}async count(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=this.supabase.from(e).select("*",{count:"exact",head:!0});Object.entries(t).forEach(e=>{let[t,a]=e;r=r.eq(t,a)});let{count:a,error:i}=await r;if(i)throw console.error("Database count error:",i),i;return a||0}constructor(){this.supabase=h()}}new f;let g=(0,i.createContext)(void 0);function y(e){let{children:t}=e,[r,n]=(0,i.useState)(null),[o,l]=(0,i.useState)(null),[u,c]=(0,i.useState)(!0),[d,f]=(0,i.useState)(null),[y,p]=(0,i.useState)(null),E=h();(0,i.useEffect)(()=>{(async()=>{let{data:{session:e},error:t}=await E.auth.getSession();t?console.error("Error getting session:",t):await v(e),c(!1)})();let{data:{subscription:e}}=E.auth.onAuthStateChange(async(e,t)=>{var r;console.log("Auth state changed:",e,null==t||null==(r=t.user)?void 0:r.email),await v(t),c(!1)});return()=>{e.unsubscribe()}},[E.auth]);let v=async e=>{if(l(e),null==e?void 0:e.user){let t=await P(e.user);n({id:e.user.id,email:e.user.email||"",role:t,createdAt:new Date(e.user.created_at),updatedAt:new Date(e.user.updated_at||e.user.created_at)}),t===s.gG.DOCTOR?(f(e.user.id),p(null)):t===s.gG.PATIENT?(p(e.user.id),f(await w.getCurrentDoctorId())):(f(null),p(null))}else n(null),f(null),p(null)},P=async e=>{var t,r;let a=(null==(t=e.user_metadata)?void 0:t.role)||(null==(r=e.app_metadata)?void 0:r.role);if(a)return a;try{let{data:t}=await E.from("platform_admins").select("id").eq("id",e.id).single();if(t)return s.gG.SUPER_ADMIN}catch(e){}try{let{data:t}=await E.from("doctors").select("id").eq("id",e.id).single();if(t)return s.gG.DOCTOR}catch(e){}try{let{data:t}=await E.from("patients").select("id").eq("id",e.id).single();if(t)return s.gG.PATIENT}catch(e){}return s.gG.PATIENT},m=e=>(null==r?void 0:r.role)===e,U={user:r,session:o,loading:u,login:async(e,t)=>{c(!0);try{let r=await w.signInWithPassword(e,t);if(r.error)throw r.error;return r}catch(e){throw console.error("Login failed:",e),e}finally{c(!1)}},loginWithGoogle:async()=>{c(!0);try{let e=await w.signInWithGoogle();if(e.error)throw e.error;return e}catch(e){throw console.error("Google login failed:",e),e}finally{c(!1)}},logout:async()=>{c(!0);try{let{error:e}=await w.signOut();if(e)throw e}catch(e){throw console.error("Logout failed:",e),e}finally{c(!1)}},register:async e=>{c(!0);try{let t=await w.signUp(e.email,e.password,{role:e.role,...e.metadata});if(t.error)throw t.error;return t}catch(e){throw console.error("Registration failed:",e),e}finally{c(!1)}},resetPassword:async e=>{try{let t=await w.resetPassword(e);if(t.error)throw t.error;return t}catch(e){throw console.error("Password reset failed:",e),e}},isAuthenticated:!!r,hasRole:m,isSuperAdmin:m(s.gG.SUPER_ADMIN),isDoctor:m(s.gG.DOCTOR),isPatient:m(s.gG.PATIENT),doctorId:d,patientId:y};return(0,a.jsx)(g.Provider,{value:U,children:t})}function p(){let e=(0,i.useContext)(g);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},3717:(e,t,r)=>{r.d(t,{Sj:()=>i,gG:()=>a});var a=function(e){return e.SUPER_ADMIN="super_admin",e.DOCTOR="doctor",e.PATIENT="patient",e}({}),i=function(e){return e.USD="USD",e.EGP="EGP",e.EUR="EUR",e}({})}}]);