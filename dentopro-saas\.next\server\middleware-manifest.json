{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pOiyKTpSK1bOjw0PZye+iSs0YWUF5tcRbMoGCU1I/Pk=", "__NEXT_PREVIEW_MODE_ID": "d2a6028133cb8bc48887b8cefb15b82c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a999aeae0e2db9027cc5c49372ec54ffd4e8abcbf596ae06075e5da158b660bf", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6e99785c584c24cc4977567c1d1fbeb93673aefa123fa8ca1c9bba330d5bcb3f"}}}, "functions": {}, "sortedMiddleware": ["/"]}