import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase-server';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const supabase = createServerSupabaseClient();

    // Check if 2FA is currently enabled
    const { data: currentSettings, error: fetchError } = await supabase
      .from('user_2fa_settings')
      .select('*')
      .eq('user_id', userId)
      .eq('is_enabled', true)
      .single();

    if (fetchError || !currentSettings) {
      return NextResponse.json(
        { error: '2FA is not currently enabled for this user' },
        { status: 400 }
      );
    }

    // Disable 2FA
    const { error: disableError } = await supabase
      .from('user_2fa_settings')
      .update({
        is_enabled: false,
        disabled_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);

    if (disableError) {
      console.error('2FA disable error:', disableError);
      return NextResponse.json(
        { error: 'Failed to disable 2FA' },
        { status: 500 }
      );
    }

    // Clean up any pending verification codes
    await supabase
      .from('two_factor_codes')
      .delete()
      .eq('user_id', userId);

    return NextResponse.json({
      success: true,
      enabled: false,
      message: 'Two-factor authentication disabled successfully'
    });

  } catch (error) {
    console.error('2FA disable error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
