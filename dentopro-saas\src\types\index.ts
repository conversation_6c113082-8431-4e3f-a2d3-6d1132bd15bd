// Core User Types
export interface User {
  id: string;
  email: string;
  role: UserRole;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  DOCTOR = 'doctor',
  PATIENT = 'patient'
}

// Super Admin Types
export interface SuperAdmin extends User {
  role: UserRole.SUPER_ADMIN;
  permissions: SuperAdminPermissions;
}

export interface SuperAdminPermissions {
  viewAllDoctors: boolean;
  approveDoctorRegistrations: boolean;
  suspendDoctorAccounts: boolean;
  manageDoctorSubscriptions: boolean;
  viewPlatformRevenue: boolean;
  managePricing: boolean;
  processRefunds: boolean;
  viewPaymentHistory: boolean;
  manageFeatureFlags: boolean;
  controlSystemUpdates: boolean;
  viewSystemLogs: boolean;
  manageDomainLicenses: boolean;
  viewPlatformAnalytics: boolean;
  monitorSystemHealth: boolean;
  trackUsageMetrics: boolean;
  generateReports: boolean;
}

// Doctor Types
export interface Doctor extends User {
  role: UserRole.DOCTOR;
  fullName: string;
  licenseNumber: string;
  clinicName: string;
  phone?: string;
  whatsapp?: string;
  subscriptionPlan: SubscriptionPlan;
  subscriptionStatus: SubscriptionStatus;
  domain?: string;
  trialEndDate?: Date;
  approvedBy?: string;
  clinicSettings?: ClinicSettings;
}

export enum SubscriptionPlan {
  BASIC = 'basic',
  PREMIUM = 'premium'
}

export enum SubscriptionStatus {
  TRIAL = 'trial',
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  CANCELLED = 'cancelled'
}

export interface ClinicSettings {
  id: string;
  doctorId: string;
  timezone: string;
  language: Language;
  currency: Currency;
  workingHours: WorkingHours;
  appointmentDuration: number; // in minutes
  allowOnlineBooking: boolean;
  requirePatientVerification: boolean;
  autoReminders: boolean;
  whatsappNotifications: boolean;
  emailNotifications: boolean;
}

export enum Language {
  ENGLISH = 'en',
  ARABIC = 'ar',
  FRENCH = 'fr'
}

export enum Currency {
  USD = 'USD',
  EGP = 'EGP',
  EUR = 'EUR'
}

export interface WorkingHours {
  monday: DaySchedule;
  tuesday: DaySchedule;
  wednesday: DaySchedule;
  thursday: DaySchedule;
  friday: DaySchedule;
  saturday: DaySchedule;
  sunday: DaySchedule;
}

export interface DaySchedule {
  isOpen: boolean;
  startTime: string; // HH:mm format
  endTime: string; // HH:mm format
  breakStart?: string;
  breakEnd?: string;
}

// Patient Types
export interface Patient extends User {
  role: UserRole.PATIENT;
  doctorId: string;
  fullName: string;
  phone: string;
  dateOfBirth: Date;
  gender: Gender;
  address?: string;
  emergencyContact?: EmergencyContact;
  medicalHistory?: MedicalHistory;
  dentalHistory?: DentalHistory;
  allergies?: string[];
  medications?: string[];
  isVerified: boolean;
}

export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other'
}

export interface EmergencyContact {
  name: string;
  relationship: string;
  phone: string;
}

export interface MedicalHistory {
  conditions: string[];
  surgeries: string[];
  medications: string[];
  allergies: string[];
  notes?: string;
}

export interface DentalHistory {
  previousTreatments: string[];
  currentIssues: string[];
  lastVisit?: Date;
  notes?: string;
}

// Appointment Types
export interface Appointment {
  id: string;
  doctorId: string;
  patientId: string;
  dateTime: Date;
  duration: number; // in minutes
  type: AppointmentType;
  status: AppointmentStatus;
  notes?: string;
  reminderSent: boolean;
  source: AppointmentSource;
  createdAt: Date;
  updatedAt: Date;
}

export enum AppointmentType {
  CONSULTATION = 'consultation',
  CLEANING = 'cleaning',
  FILLING = 'filling',
  ROOT_CANAL = 'root_canal',
  EXTRACTION = 'extraction',
  CHECKUP = 'checkup',
  EMERGENCY = 'emergency',
  FOLLOW_UP = 'follow_up'
}

export enum AppointmentStatus {
  SCHEDULED = 'scheduled',
  CONFIRMED = 'confirmed',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  NO_SHOW = 'no_show'
}

export enum AppointmentSource {
  DOCTOR = 'doctor',
  PATIENT_ONLINE = 'patient_online',
  PHONE = 'phone',
  WALK_IN = 'walk_in'
}

// Treatment Types
export interface Treatment {
  id: string;
  doctorId: string;
  patientId: string;
  appointmentId: string;
  procedure: string;
  toothNumber?: string;
  surface?: string;
  notes: string;
  cost: number;
  materialsUsed: MaterialUsage[];
  beforeImages?: string[];
  afterImages?: string[];
  painScore?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface MaterialUsage {
  materialId: string;
  quantity: number;
  unit: string;
}

// Inventory Types
export interface Material {
  id: string;
  doctorId: string;
  name: string;
  category: MaterialCategory;
  unit: string;
  currentStock: number;
  lowThreshold: number;
  cost: number;
  supplier?: string;
  expiryDate?: Date;
  batchNumber?: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum MaterialCategory {
  FILLING = 'filling',
  ANESTHETIC = 'anesthetic',
  CLEANING = 'cleaning',
  SURGICAL = 'surgical',
  IMPRESSION = 'impression',
  PROTECTIVE = 'protective',
  OTHER = 'other'
}

export interface MaterialLog {
  id: string;
  materialId: string;
  type: MaterialLogType;
  quantity: number;
  reason: string;
  reference?: string; // appointment ID or supplier info
  createdAt: Date;
}

export enum MaterialLogType {
  STOCK_IN = 'stock_in',
  STOCK_OUT = 'stock_out',
  ADJUSTMENT = 'adjustment',
  EXPIRED = 'expired',
  DAMAGED = 'damaged'
}

// Prescription Types
export interface Prescription {
  id: string;
  doctorId: string;
  patientId: string;
  appointmentId?: string;
  medications: Medication[];
  notes?: string;
  qrCode: string;
  issuedAt: Date;
  validUntil: Date;
}

export interface Medication {
  name: string;
  dosage: string;
  frequency: string;
  duration: string;
  instructions?: string;
}

// Invoice Types
export interface Invoice {
  id: string;
  doctorId: string;
  patientId: string;
  appointmentId?: string;
  invoiceNumber: string;
  items: InvoiceItem[];
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
  status: InvoiceStatus;
  dueDate: Date;
  paidAt?: Date;
  paymentMethod?: PaymentMethod;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface InvoiceItem {
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

export enum InvoiceStatus {
  DRAFT = 'draft',
  SENT = 'sent',
  PAID = 'paid',
  OVERDUE = 'overdue',
  CANCELLED = 'cancelled'
}

export enum PaymentMethod {
  CASH = 'cash',
  CARD = 'card',
  BANK_TRANSFER = 'bank_transfer',
  INSURANCE = 'insurance',
  ONLINE = 'online'
}

// Subscription Types
export interface Subscription {
  id: string;
  doctorId: string;
  planType: SubscriptionPlan;
  amount: number;
  status: SubscriptionStatus;
  stripeSubscriptionId?: string;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  trialEnd?: Date;
  cancelledAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Platform Analytics Types
export interface PlatformAnalytics {
  id: string;
  metricType: string;
  value: number;
  doctorId?: string;
  recordedAt: Date;
}

export interface PlatformStats {
  totalDoctors: number;
  activeDoctors: number;
  trialDoctors: number;
  monthlyRevenue: number;
  totalPatients: number;
  totalAppointments: number;
  systemUptime: string;
  averageClinicUsage: number;
}

// License Tracking Types
export interface LicenseTracking {
  id: string;
  doctorId: string;
  domain: string;
  lastActive: Date;
  usageStats: UsageStats;
  violations?: LicenseViolation[];
}

export interface UsageStats {
  dailyLogins: number;
  patientsManaged: number;
  appointmentsScheduled: number;
  invoicesGenerated: number;
}

export interface LicenseViolation {
  type: ViolationType;
  description: string;
  detectedAt: Date;
  resolved: boolean;
}

export enum ViolationType {
  DOMAIN_MISMATCH = 'domain_mismatch',
  MULTIPLE_INSTANCES = 'multiple_instances',
  UNAUTHORIZED_ACCESS = 'unauthorized_access',
  RESALE_ATTEMPT = 'resale_attempt'
}

// System Log Types
export interface SystemLog {
  id: string;
  level: LogLevel;
  message: string;
  userId?: string;
  ipAddress?: string;
  userAgent?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
}

export enum LogLevel {
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  DEBUG = 'debug'
}

// Notification Types
export interface Notification {
  id: string;
  recipientType: UserRole;
  recipientId: string;
  type: NotificationType;
  title: string;
  message: string;
  status: NotificationStatus;
  metadata?: Record<string, any>;
  createdAt: Date;
  readAt?: Date;
}

export enum NotificationType {
  APPOINTMENT_REMINDER = 'appointment_reminder',
  LOW_STOCK = 'low_stock',
  PAYMENT_DUE = 'payment_due',
  SYSTEM_ALERT = 'system_alert',
  NEW_PATIENT = 'new_patient',
  SUBSCRIPTION_EXPIRY = 'subscription_expiry'
}

export enum NotificationStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed'
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface DoctorRegistrationForm {
  fullName: string;
  email: string;
  licenseNumber: string;
  clinicName: string;
  phone: string;
  whatsapp?: string;
  subscriptionPlan: SubscriptionPlan;
}

export interface PatientRegistrationForm {
  fullName: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  gender: Gender;
  address?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  medicalHistory?: string;
  allergies?: string;
}

export interface AppointmentBookingForm {
  patientId?: string;
  patientName?: string;
  patientPhone?: string;
  patientEmail?: string;
  appointmentType: AppointmentType;
  preferredDate: string;
  preferredTime: string;
  notes?: string;
}
