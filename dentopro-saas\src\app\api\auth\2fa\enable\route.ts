import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase-server';
import { TwoFactorAuth } from '@/lib/two-factor-auth';

export async function POST(request: NextRequest) {
  try {
    const { userId, phoneNumber } = await request.json();

    if (!userId || !phoneNumber) {
      return NextResponse.json(
        { error: 'User ID and phone number are required' },
        { status: 400 }
      );
    }

    // Validate phone number format
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    if (!phoneRegex.test(phoneNumber)) {
      return NextResponse.json(
        { error: 'Invalid phone number format. Use international format (+**********)' },
        { status: 400 }
      );
    }

    const supabase = createServerSupabaseClient();

    // Check if user exists
    const { data: user, error: userError } = await supabase
      .from('doctors')
      .select('id, email, full_name')
      .eq('id', userId)
      .single();

    if (userError || !user) {
      // Try patients table
      const { data: patient, error: patientError } = await supabase
        .from('patients')
        .select('id, email, full_name')
        .eq('id', userId)
        .single();

      if (patientError || !patient) {
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        );
      }
    }

    // Generate verification code
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();

    // Store verification code in database (expires in 10 minutes)
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000);
    
    const { error: insertError } = await supabase
      .from('two_factor_codes')
      .upsert({
        user_id: userId,
        phone_number: phoneNumber,
        verification_code: verificationCode,
        expires_at: expiresAt.toISOString(),
        is_verified: false,
        created_at: new Date().toISOString()
      });

    if (insertError) {
      console.error('Database error:', insertError);
      return NextResponse.json(
        { error: 'Failed to store verification code' },
        { status: 500 }
      );
    }

    // Send SMS using Twilio (or your preferred SMS service)
    try {
      await TwoFactorAuth.sendSMS(phoneNumber, verificationCode);
    } catch (smsError) {
      console.error('SMS sending failed:', smsError);
      return NextResponse.json(
        { error: 'Failed to send verification code' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Verification code sent to your phone',
      phoneNumber: phoneNumber.replace(/(\+\d{1,3})\d{4,}(\d{4})/, '$1****$2') // Mask phone number
    });

  } catch (error) {
    console.error('2FA enable error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
