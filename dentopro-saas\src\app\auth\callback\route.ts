// Auth callback handler for OAuth providers
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { Database } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const error = requestUrl.searchParams.get('error');
  const errorDescription = requestUrl.searchParams.get('error_description');

  // Handle OAuth errors
  if (error) {
    console.error('OAuth error:', error, errorDescription);
    return NextResponse.redirect(
      new URL(`/auth/login?error=${encodeURIComponent(errorDescription || error)}`, requestUrl.origin)
    );
  }

  if (code) {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient<Database>({ cookies: () => cookieStore });

    try {
      // Exchange the code for a session
      const { data, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code);

      if (exchangeError) {
        console.error('Code exchange error:', exchangeError);
        return NextResponse.redirect(
          new URL(`/auth/login?error=${encodeURIComponent(exchangeError.message)}`, requestUrl.origin)
        );
      }

      if (data.user) {
        // Determine user role and setup profile if needed
        const userRole = await determineUserRole(supabase, data.user);
        
        // Update user metadata with role
        if (userRole) {
          await supabase.auth.updateUser({
            data: { role: userRole }
          });
        }

        // Redirect based on user role
        const redirectUrl = getRedirectUrlByRole(userRole, requestUrl.origin);
        return NextResponse.redirect(redirectUrl);
      }
    } catch (error) {
      console.error('Callback processing error:', error);
      return NextResponse.redirect(
        new URL('/auth/login?error=Authentication failed', requestUrl.origin)
      );
    }
  }

  // If no code, redirect to login
  return NextResponse.redirect(new URL('/auth/login', requestUrl.origin));
}

// Determine user role based on email or existing records
async function determineUserRole(supabase: any, user: any): Promise<string> {
  try {
    // Check if user exists in platform_admins
    const { data: admin } = await supabase
      .from('platform_admins')
      .select('id')
      .eq('email', user.email)
      .single();
    
    if (admin) {
      return 'super_admin';
    }

    // Check if user exists in doctors
    const { data: doctor } = await supabase
      .from('doctors')
      .select('id')
      .eq('email', user.email)
      .single();
    
    if (doctor) {
      return 'doctor';
    }

    // Check if user exists in patients
    const { data: patient } = await supabase
      .from('patients')
      .select('id')
      .eq('email', user.email)
      .single();
    
    if (patient) {
      return 'patient';
    }

    // For new users, check email domain or create as patient by default
    if (user.email?.endsWith('@dentopro.com')) {
      return 'super_admin';
    }

    // Default to patient for new OAuth users
    return 'patient';
  } catch (error) {
    console.error('Error determining user role:', error);
    return 'patient'; // Default fallback
  }
}

// Get redirect URL based on user role
function getRedirectUrlByRole(role: string, origin: string): string {
  switch (role) {
    case 'super_admin':
      return `${origin}/admin`;
    case 'doctor':
      return `${origin}/dashboard`;
    case 'patient':
      return `${origin}/patient`;
    default:
      return `${origin}/auth/complete-profile`;
  }
}
