exports.id=415,exports.ids=[415],exports.modules={1188:(a,b,c)=>{"use strict";c.d(b,{ThemeProvider:()=>g});var d=c(60687),e=c(43210);let f=(0,e.createContext)({theme:"system",setTheme:()=>null});function g({children:a,defaultTheme:b="system",storageKey:c="dentopro-ui-theme",...g}){let[h,i]=(0,e.useState)(()=>localStorage?.getItem(c)||b);return(0,d.jsx)(f.Provider,{...g,value:{theme:h,setTheme:a=>{localStorage?.setItem(c,a),i(a)}},children:a})}},22670:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},26371:(a,b,c)=>{"use strict";c.d(b,{AuthProvider:()=>p,A:()=>q});var d=c(60687),e=c(43210),f=c(78259),g=c(60463),h=c(29605);let i=process.env.NEXT_PUBLIC_SUPABASE_URL,j=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,k=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!i||!j)throw Error("Missing Supabase environment variables. Please check your .env file.");(0,g.UU)(i,j,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0}}),(0,g.UU)(i,k||j,{auth:{autoRefreshToken:!1,persistSession:!1}});let l=()=>(0,h.createBrowserClient)(i,j);class m{static async getCurrentUser(){let a=l(),{data:{user:b},error:c}=await a.auth.getUser();if(c||!b)return null;let d=b.user_metadata?.role||b.app_metadata?.role||"patient";return{...b,role:d}}static async hasRole(a){let b=await this.getCurrentUser();return b?.role===a}static async getCurrentDoctorId(){let a=await this.getCurrentUser();if(a?.role==="doctor")return a.id;if(a?.role==="patient"){let b=l(),{data:c}=await b.from("patients").select("doctor_id").eq("id",a.id).single();return c?.doctor_id||null}return null}static async getCurrentPatientId(){let a=await this.getCurrentUser();return a?.role==="patient"?a.id:null}static async signInWithPassword(a,b){let c=l();return await c.auth.signInWithPassword({email:a,password:b})}static async signInWithGoogle(){let a=l();return await a.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback`}})}static async signUp(a,b,c){let d=l();return await d.auth.signUp({email:a,password:b,options:{data:c}})}static async signOut(){let a=l();return await a.auth.signOut()}static async updateUserMetadata(a){let b=l();return await b.auth.updateUser({data:a})}static async resetPassword(a){let b=l();return await b.auth.resetPasswordForEmail(a,{redirectTo:`${window.location.origin}/auth/reset-password`})}}class n{async findMany(a,b={},c={}){let d=this.supabase.from(a).select(c.select||"*");if(Object.entries(b).forEach(([a,b])=>{d=d.eq(a,b)}),c.orderBy){let[a,b]=c.orderBy.split(" ");d=d.order(a,{ascending:"desc"!==b})}c.limit&&(d=d.limit(c.limit)),c.offset&&(d=d.range(c.offset,c.offset+(c.limit||10)-1));let{data:e,error:f}=await d;if(f)throw console.error("Database query error:",f),f;return e}async findOne(a,b){let c=this.supabase.from(a).select("*");Object.entries(b).forEach(([a,b])=>{c=c.eq(a,b)});let{data:d,error:e}=await c.single();if(e&&"PGRST116"!==e.code)throw console.error("Database query error:",e),e;return d}async insert(a,b){let{data:c,error:d}=await this.supabase.from(a).insert(b).select().single();if(d)throw console.error("Database insert error:",d),d;return c}async update(a,b,c){let d=this.supabase.from(a).update(b);Object.entries(c).forEach(([a,b])=>{d=d.eq(a,b)});let{data:e,error:f}=await d.select().single();if(f&&"PGRST116"!==f.code)throw console.error("Database update error:",f),f;return e}async delete(a,b){let c=this.supabase.from(a).delete();Object.entries(b).forEach(([a,b])=>{c=c.eq(a,b)});let{error:d}=await c;if(d)throw console.error("Database delete error:",d),d}async count(a,b={}){let c=this.supabase.from(a).select("*",{count:"exact",head:!0});Object.entries(b).forEach(([a,b])=>{c=c.eq(a,b)});let{count:d,error:e}=await c;if(e)throw console.error("Database count error:",e),e;return d||0}constructor(){this.supabase=l()}}new n;let o=(0,e.createContext)(void 0);function p({children:a}){let[b,c]=(0,e.useState)(null),[g,h]=(0,e.useState)(null),[i,j]=(0,e.useState)(!0),[k,n]=(0,e.useState)(null),[p,q]=(0,e.useState)(null);l();let r=a=>b?.role===a,s={user:b,session:g,loading:i,login:async(a,b)=>{j(!0);try{let c=await m.signInWithPassword(a,b);if(c.error)throw c.error;return c}catch(a){throw console.error("Login failed:",a),a}finally{j(!1)}},loginWithGoogle:async()=>{j(!0);try{let a=await m.signInWithGoogle();if(a.error)throw a.error;return a}catch(a){throw console.error("Google login failed:",a),a}finally{j(!1)}},logout:async()=>{j(!0);try{let{error:a}=await m.signOut();if(a)throw a}catch(a){throw console.error("Logout failed:",a),a}finally{j(!1)}},register:async a=>{j(!0);try{let b=await m.signUp(a.email,a.password,{role:a.role,...a.metadata});if(b.error)throw b.error;return b}catch(a){throw console.error("Registration failed:",a),a}finally{j(!1)}},resetPassword:async a=>{try{let b=await m.resetPassword(a);if(b.error)throw b.error;return b}catch(a){throw console.error("Password reset failed:",a),a}},isAuthenticated:!!b,hasRole:r,isSuperAdmin:r(f.gG.SUPER_ADMIN),isDoctor:r(f.gG.DOCTOR),isPatient:r(f.gG.PATIENT),doctorId:k,patientId:p};return(0,d.jsx)(o.Provider,{value:s,children:a})}function q(){let a=(0,e.useContext)(o);if(void 0===a)throw Error("useAuth must be used within an AuthProvider");return a}},36582:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},39727:()=>{},41374:(a,b,c)=>{Promise.resolve().then(c.bind(c,68462)),Promise.resolve().then(c.bind(c,49567))},47990:()=>{},49567:(a,b,c)=>{"use strict";c.d(b,{AuthProvider:()=>e});var d=c(61369);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\hooks\\useAuth.tsx","AuthProvider");(0,d.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\hooks\\useAuth.tsx","useAuth")},61135:()=>{},68462:(a,b,c)=>{"use strict";c.d(b,{ThemeProvider:()=>e});var d=c(61369);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\contexts\\ThemeContext.tsx","ThemeProvider");(0,d.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\contexts\\ThemeContext.tsx","useTheme")},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},78259:(a,b,c)=>{"use strict";c.d(b,{Sj:()=>e,gG:()=>d});var d=function(a){return a.SUPER_ADMIN="super_admin",a.DOCTOR="doctor",a.PATIENT="patient",a}({}),e=function(a){return a.USD="USD",a.EGP="EGP",a.EUR="EUR",a}({})},81542:(a,b,c)=>{Promise.resolve().then(c.bind(c,1188)),Promise.resolve().then(c.bind(c,26371))},86890:(a,b,c)=>{"use strict";c.d(b,{C3:()=>d,P:()=>f,VK:()=>e});let d="DentoPro",e={USD:{code:"USD",symbol:"$",name:"US Dollar",locale:"en-US"},EGP:{code:"EGP",symbol:"ج.م",name:"Egyptian Pound",locale:"ar-EG"},EUR:{code:"EUR",symbol:"€",name:"Euro",locale:"en-EU"}},f={SOFTWARE_WATERMARK:`\xa9 ${new Date().getFullYear()} DentoPro - Licensed to Single Doctor Use Only`,DOMAIN_BINDING_REQUIRED:!0,MAX_CONCURRENT_SESSIONS:3,USAGE_TRACKING_ENABLED:!0,ANTI_RESALE_PROTECTION:!0}},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>i,metadata:()=>h});var d=c(37413);c(61135);var e=c(49567),f=c(68462),g=c(86890);let h={title:`${g.C3} - Complete Dental Clinic Management`,description:"Streamline your dental practice with our comprehensive SaaS platform. Manage patients, appointments, treatments, inventory, and more - all in one place.",keywords:"dental clinic management, dental software, patient management, appointment scheduling, dental SaaS",authors:[{name:"DentoPro Team"}],viewport:"width=device-width, initial-scale=1",robots:"index, follow",openGraph:{title:`${g.C3} - Complete Dental Clinic Management`,description:"Streamline your dental practice with our comprehensive SaaS platform.",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:`${g.C3} - Complete Dental Clinic Management`,description:"Streamline your dental practice with our comprehensive SaaS platform."}};function i({children:a}){return(0,d.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,d.jsx)("body",{className:"antialiased",children:(0,d.jsx)(f.ThemeProvider,{defaultTheme:"system",storageKey:"dentopro-ui-theme",children:(0,d.jsx)(e.AuthProvider,{children:a})})})})}}};