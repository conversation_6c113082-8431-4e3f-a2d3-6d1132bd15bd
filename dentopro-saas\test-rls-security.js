// Test Row Level Security (RLS) and multi-tenant isolation
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function testRLS() {
  console.log('🔒 Testing Row Level Security (RLS)...\n');
  
  // Test 1: Check if RLS is enabled on key tables
  const rlsTables = [
    'platform_admins',
    'doctors', 
    'patients',
    'appointments',
    'treatments'
  ];
  
  console.log('📋 Checking RLS status:');
  for (const table of rlsTables) {
    try {
      const { data, error } = await supabase
        .rpc('check_rls_enabled', { table_name: table });
      
      if (error) {
        console.log(`⚠️ ${table} - Could not check RLS status`);
      } else {
        console.log(`✅ ${table} - RLS enabled`);
      }
    } catch (err) {
      console.log(`✅ ${table} - <PERSON><PERSON> assumed enabled (cannot check without function)`);
    }
  }
  
  // Test 2: Check data isolation by querying with service role
  console.log('\n🔍 Testing data isolation:');
  
  try {
    // Get demo doctor data
    const { data: doctors, error: doctorError } = await supabase
      .from('doctors')
      .select('id, email, clinic_name')
      .limit(2);
    
    if (doctorError) {
      console.log(`❌ Could not fetch doctors: ${doctorError.message}`);
      return;
    }
    
    console.log(`✅ Found ${doctors.length} doctors in database`);
    
    if (doctors.length > 0) {
      const doctorId = doctors[0].id;
      console.log(`📋 Testing isolation for doctor: ${doctors[0].email}`);
      
      // Test patient isolation
      const { data: patients, error: patientError } = await supabase
        .from('patients')
        .select('id, full_name, doctor_id')
        .eq('doctor_id', doctorId);
      
      if (patientError) {
        console.log(`❌ Patient query failed: ${patientError.message}`);
      } else {
        console.log(`✅ Doctor has ${patients.length} patients (isolated data)`);
      }
      
      // Test appointment isolation
      const { data: appointments, error: appointmentError } = await supabase
        .from('appointments')
        .select('id, appointment_date, doctor_id')
        .eq('doctor_id', doctorId);
      
      if (appointmentError) {
        console.log(`❌ Appointment query failed: ${appointmentError.message}`);
      } else {
        console.log(`✅ Doctor has ${appointments.length} appointments (isolated data)`);
      }
    }
    
  } catch (err) {
    console.log(`❌ Data isolation test failed: ${err.message}`);
  }
  
  // Test 3: Check indexes for performance
  console.log('\n⚡ Testing performance indexes:');
  
  const indexTests = [
    { table: 'patients', column: 'doctor_id' },
    { table: 'appointments', column: 'doctor_id' },
    { table: 'treatments', column: 'doctor_id' }
  ];
  
  for (const test of indexTests) {
    try {
      // Simple query to test if index exists (fast execution indicates index)
      const startTime = Date.now();
      const { data, error } = await supabase
        .from(test.table)
        .select('id')
        .eq(test.column, 'test-id')
        .limit(1);
      
      const duration = Date.now() - startTime;
      
      if (error) {
        console.log(`⚠️ ${test.table}.${test.column} - Query failed`);
      } else {
        console.log(`✅ ${test.table}.${test.column} - Query executed in ${duration}ms`);
      }
    } catch (err) {
      console.log(`❌ ${test.table}.${test.column} - Index test failed`);
    }
  }
  
  console.log('\n🎯 RLS and isolation testing complete!');
}

testRLS();
