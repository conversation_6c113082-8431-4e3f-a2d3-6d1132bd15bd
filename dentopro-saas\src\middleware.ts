// Next.js middleware for authentication and authorization
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { Database } from '@/lib/supabase';

// Define protected routes and their required roles
const PROTECTED_ROUTES = {
  // Super Admin routes
  '/admin': ['super_admin'],
  '/admin/*': ['super_admin'],
  
  // Doctor routes
  '/dashboard': ['doctor', 'super_admin'],
  '/dashboard/*': ['doctor', 'super_admin'],
  '/patients': ['doctor', 'super_admin'],
  '/patients/*': ['doctor', 'super_admin'],
  '/appointments': ['doctor', 'super_admin'],
  '/appointments/*': ['doctor', 'super_admin'],
  '/treatments': ['doctor', 'super_admin'],
  '/treatments/*': ['doctor', 'super_admin'],
  '/inventory': ['doctor', 'super_admin'],
  '/inventory/*': ['doctor', 'super_admin'],
  '/prescriptions': ['doctor', 'super_admin'],
  '/prescriptions/*': ['doctor', 'super_admin'],
  '/invoices': ['doctor', 'super_admin'],
  '/invoices/*': ['doctor', 'super_admin'],
  '/settings': ['doctor', 'super_admin'],
  '/settings/*': ['doctor', 'super_admin'],
  
  // Patient routes
  '/patient': ['patient'],
  '/patient/*': ['patient'],
  '/my-appointments': ['patient'],
  '/my-treatments': ['patient'],
  '/my-prescriptions': ['patient'],
  '/my-invoices': ['patient'],
  
  // API routes
  '/api/admin/*': ['super_admin'],
  '/api/doctor/*': ['doctor', 'super_admin'],
  '/api/patient/*': ['patient', 'doctor', 'super_admin'],
};

// Public routes that don't require authentication
const PUBLIC_ROUTES = [
  '/',
  '/auth/login',
  '/auth/register',
  '/auth/callback',
  '/auth/reset-password',
  '/auth/verify-email',
  '/public/*',
  '/api/public/*',
  '/api/auth/*',
  '/api/webhooks/*',
  '/_next/*',
  '/favicon.ico',
  '/robots.txt',
  '/sitemap.xml'
];

// Get user role from user metadata or database
async function getUserRole(supabase: any, userId: string): Promise<string | null> {
  try {
    // First check user metadata
    const { data: { user } } = await supabase.auth.getUser();
    if (user?.user_metadata?.role) {
      return user.user_metadata.role;
    }
    if (user?.app_metadata?.role) {
      return user.app_metadata.role;
    }

    // Check platform_admins table
    const { data: admin } = await supabase
      .from('platform_admins')
      .select('id')
      .eq('id', userId)
      .single();
    
    if (admin) {
      return 'super_admin';
    }

    // Check doctors table
    const { data: doctor } = await supabase
      .from('doctors')
      .select('id')
      .eq('id', userId)
      .single();
    
    if (doctor) {
      return 'doctor';
    }

    // Check patients table
    const { data: patient } = await supabase
      .from('patients')
      .select('id')
      .eq('id', userId)
      .single();
    
    if (patient) {
      return 'patient';
    }

    return null;
  } catch (error) {
    console.error('Error getting user role:', error);
    return null;
  }
}

// Check if route is public
function isPublicRoute(pathname: string): boolean {
  return PUBLIC_ROUTES.some(route => {
    if (route.endsWith('/*')) {
      return pathname.startsWith(route.slice(0, -2));
    }
    return pathname === route;
  });
}

// Check if user has required role for route
function hasRequiredRole(pathname: string, userRole: string): boolean {
  for (const [route, allowedRoles] of Object.entries(PROTECTED_ROUTES)) {
    if (route.endsWith('/*')) {
      if (pathname.startsWith(route.slice(0, -2))) {
        return allowedRoles.includes(userRole);
      }
    } else if (pathname === route) {
      return allowedRoles.includes(userRole);
    }
  }
  
  // If route is not in protected routes, allow access
  return true;
}

// Get redirect URL based on user role
function getRedirectUrl(role: string, request: NextRequest): string {
  const baseUrl = request.nextUrl.origin;
  
  switch (role) {
    case 'super_admin':
      return `${baseUrl}/admin`;
    case 'doctor':
      return `${baseUrl}/dashboard`;
    case 'patient':
      return `${baseUrl}/patient`;
    default:
      return `${baseUrl}/auth/login`;
  }
}

export async function middleware(request: NextRequest) {
  try {
    const response = NextResponse.next();
    const pathname = request.nextUrl.pathname;

    // Skip middleware for static files and API routes that don't need auth
    if (
      pathname.startsWith('/_next/') ||
      pathname.startsWith('/api/webhooks/') ||
      pathname.startsWith('/api/public/') ||
      pathname.includes('.') // Static files
    ) {
      return response;
    }

    // Create Supabase client
    const supabase = createMiddlewareClient<Database>({ req: request, res: response });

    // Get session
    const { data: { session }, error } = await supabase.auth.getSession();

    // If there's an error getting session, redirect to login
    if (error) {
      console.error('Session error:', error);
      return NextResponse.redirect(new URL('/auth/login', request.url));
    }

    // Handle public routes
    if (isPublicRoute(pathname)) {
      // If user is already authenticated and trying to access auth pages, redirect to dashboard
      if (session && (pathname.startsWith('/auth/') || pathname === '/')) {
        const userRole = await getUserRole(supabase, session.user.id);
        if (userRole) {
          return NextResponse.redirect(new URL(getRedirectUrl(userRole, request), request.url));
        }
      }
      return response;
    }

    // Check if user is authenticated
    if (!session) {
      // Store the attempted URL to redirect after login
      const redirectUrl = new URL('/auth/login', request.url);
      redirectUrl.searchParams.set('redirectTo', pathname);
      return NextResponse.redirect(redirectUrl);
    }

    // Get user role
    const userRole = await getUserRole(supabase, session.user.id);
    
    if (!userRole) {
      console.error('User role not found for user:', session.user.id);
      return NextResponse.redirect(new URL('/auth/login', request.url));
    }

    // Check if user has required role for the route
    if (!hasRequiredRole(pathname, userRole)) {
      console.warn(`Access denied: User ${session.user.id} with role ${userRole} tried to access ${pathname}`);
      
      // Redirect to appropriate dashboard based on role
      return NextResponse.redirect(new URL(getRedirectUrl(userRole, request), request.url));
    }

    // Add user info to headers for use in API routes
    response.headers.set('x-user-id', session.user.id);
    response.headers.set('x-user-role', userRole);
    response.headers.set('x-user-email', session.user.email || '');

    // Log access for audit purposes
    console.log(`Access granted: User ${session.user.id} (${userRole}) accessing ${pathname}`);

    return response;
  } catch (error) {
    console.error('Middleware error:', error);
    
    // In case of any error, redirect to login
    return NextResponse.redirect(new URL('/auth/login', request.url));
  }
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};

export default middleware;
