// Test routing fixes
console.log('🔧 Testing Routing Fixes...\n');

const fs = require('fs');
const path = require('path');

// Test 1: Check if dashboard redirect exists
console.log('📋 Test 1: Dashboard Redirect');

const dashboardRedirectPath = path.join(__dirname, 'src/app/dashboard/page.tsx');
if (fs.existsSync(dashboardRedirectPath)) {
  console.log('✅ Legacy dashboard redirect - EXISTS');
  
  const content = fs.readFileSync(dashboardRedirectPath, 'utf8');
  if (content.includes('/doctor/dashboard')) {
    console.log('✅ Redirects to correct path - /doctor/dashboard');
  } else {
    console.log('❌ Redirect path incorrect');
  }
} else {
  console.log('❌ Legacy dashboard redirect - MISSING');
}

// Test 2: Check if doctor dashboard exists
console.log('\n📋 Test 2: Doctor Dashboard');

const doctorDashboardPath = path.join(__dirname, 'src/app/doctor/dashboard/page.tsx');
if (fs.existsSync(doctorDashboardPath)) {
  console.log('✅ Doctor dashboard - EXISTS');
} else {
  console.log('❌ Doctor dashboard - MISSING');
}

// Test 3: Check login page routing
console.log('\n📋 Test 3: Login Page Routing');

const loginPath = path.join(__dirname, 'src/app/auth/login/page.tsx');
if (fs.existsSync(loginPath)) {
  const content = fs.readFileSync(loginPath, 'utf8');
  
  if (content.includes('/doctor/dashboard')) {
    console.log('✅ Login redirects to /doctor/dashboard');
  } else if (content.includes('/dashboard')) {
    console.log('⚠️ Login still redirects to old /dashboard path');
  } else {
    console.log('❌ No dashboard redirect found in login');
  }
} else {
  console.log('❌ Login page - MISSING');
}

// Test 4: Check middleware routing
console.log('\n📋 Test 4: Middleware Routing');

const middlewarePath = path.join(__dirname, 'src/middleware.ts');
if (fs.existsSync(middlewarePath)) {
  const content = fs.readFileSync(middlewarePath, 'utf8');
  
  if (content.includes('/doctor/dashboard')) {
    console.log('✅ Middleware redirects to /doctor/dashboard');
  } else {
    console.log('⚠️ Middleware may not redirect to correct path');
  }
  
  if (content.includes('getUser()')) {
    console.log('✅ Middleware uses secure getUser() method');
  } else if (content.includes('getSession()')) {
    console.log('⚠️ Middleware still uses getSession() (less secure)');
  }
} else {
  console.log('❌ Middleware - MISSING');
}

// Test 5: Check OAuth callback routing
console.log('\n📋 Test 5: OAuth Callback Routing');

const callbackPath = path.join(__dirname, 'src/app/auth/callback/route.ts');
if (fs.existsSync(callbackPath)) {
  const content = fs.readFileSync(callbackPath, 'utf8');
  
  if (content.includes('/doctor/dashboard')) {
    console.log('✅ OAuth callback redirects to /doctor/dashboard');
  } else {
    console.log('⚠️ OAuth callback may redirect to wrong path');
  }
} else {
  console.log('❌ OAuth callback - MISSING');
}

console.log('\n🎯 Routing Fix Test Complete!');

console.log('\n📋 EXPECTED BEHAVIOR:');
console.log('✅ /dashboard → redirects to → /doctor/dashboard');
console.log('✅ Login → redirects to → /doctor/dashboard');
console.log('✅ OAuth → redirects to → /doctor/dashboard');
console.log('✅ No more 404 errors');
console.log('✅ Reduced Supabase warnings');

console.log('\n🔄 TESTING STEPS:');
console.log('1. Restart dev server: npm run dev');
console.log('2. Clear browser cache: Ctrl+Shift+R');
console.log('3. Go to: http://localhost:3000');
console.log('4. Click "Doctor Login"');
console.log('5. Login with demo credentials');
console.log('6. Should redirect to /doctor/dashboard');

console.log('\n💡 DEMO CREDENTIALS:');
console.log('Email: <EMAIL>');
console.log('Password: doctor123');
