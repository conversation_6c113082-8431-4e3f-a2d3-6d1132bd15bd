// Test button functionality in browser console
// Copy and paste this into your browser console (F12) while on localhost:3000

console.log('🔘 Testing Button Functionality...\n');

// Test 1: Check if buttons exist
console.log('📋 Test 1: Button Existence');

const buttons = document.querySelectorAll('button');
console.log(`✅ Found ${buttons.length} buttons on page`);

buttons.forEach((button, index) => {
  console.log(`  Button ${index + 1}: "${button.textContent?.trim()}"`);
});

// Test 2: Check if links exist
console.log('\n📋 Test 2: Link Existence');

const links = document.querySelectorAll('a');
console.log(`✅ Found ${links.length} links on page`);

const importantLinks = Array.from(links).filter(link => 
  link.textContent?.includes('Doctor Login') || 
  link.textContent?.includes('Book Appointment') ||
  link.textContent?.includes('Start Free Trial') ||
  link.textContent?.includes('View Demo')
);

console.log(`✅ Found ${importantLinks.length} important navigation links`);

importantLinks.forEach((link, index) => {
  console.log(`  Link ${index + 1}: "${link.textContent?.trim()}" → ${link.href}`);
});

// Test 3: Check if buttons are clickable
console.log('\n📋 Test 3: Button Clickability');

buttons.forEach((button, index) => {
  const isDisabled = button.disabled;
  const hasClickHandler = button.onclick !== null;
  const isInLink = button.closest('a') !== null;
  
  console.log(`  Button ${index + 1}: ${isDisabled ? '❌ Disabled' : '✅ Enabled'}, ${hasClickHandler ? '✅ Has Handler' : isInLink ? '✅ In Link' : '⚠️ No Handler'}`);
});

// Test 4: Simulate button clicks (safely)
console.log('\n📋 Test 4: Testing Navigation');

const testNavigation = () => {
  const doctorLoginLink = Array.from(links).find(link => 
    link.textContent?.includes('Doctor Login')
  );
  
  if (doctorLoginLink) {
    console.log(`✅ Doctor Login link found: ${doctorLoginLink.href}`);
    console.log('  Click this link to test navigation');
  } else {
    console.log('❌ Doctor Login link not found');
  }
  
  const bookAppointmentLink = Array.from(links).find(link => 
    link.textContent?.includes('Book Appointment')
  );
  
  if (bookAppointmentLink) {
    console.log(`✅ Book Appointment link found: ${bookAppointmentLink.href}`);
  } else {
    console.log('❌ Book Appointment link not found');
  }
};

testNavigation();

// Test 5: Check for JavaScript errors
console.log('\n📋 Test 5: Error Check');

window.addEventListener('error', (e) => {
  console.error('❌ JavaScript Error:', e.message);
});

console.log('✅ Error listener added - any JS errors will be logged');

console.log('\n🎯 Button Functionality Test Complete!');
console.log('\n📋 Manual Tests:');
console.log('1. Click each button to verify navigation');
console.log('2. Check if pages load correctly');
console.log('3. Test form submissions');
console.log('4. Verify responsive design');

// Return test results
return {
  buttonsFound: buttons.length,
  linksFound: links.length,
  importantLinksFound: importantLinks.length,
  status: 'Test completed - check console output above'
};
