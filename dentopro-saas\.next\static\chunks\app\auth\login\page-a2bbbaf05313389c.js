(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[859],{1455:(e,r,s)=>{Promise.resolve().then(s.bind(s,6738))},6738:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>b});var t=s(5155),a=s(2115),l=s(5695),n=s(6874),o=s.n(n),i=s(4624),d=s(2085),c=s(2596),m=s(9688);new Date().getFullYear(),s(3717);let u=(0,d.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),x=a.forwardRef((e,r)=>{let{className:s,variant:a,size:l,asChild:n=!1,...o}=e,d=n?i.DX:"button";return(0,t.jsx)(d,{className:function(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return(0,m.QP)((0,c.$)(r))}(u({variant:a,size:l,className:s})),ref:r,...o})});x.displayName="Button";var h=s(1866);function b(){let[e,r]=(0,a.useState)(""),[s,n]=(0,a.useState)(""),[i,d]=(0,a.useState)(!1),[c,m]=(0,a.useState)(""),{login:u,loginWithGoogle:b,isAuthenticated:f,user:g}=(0,h.A)(),p=(0,l.useRouter)(),v=(0,l.useSearchParams)().get("redirectTo")||"/dashboard";(0,a.useEffect)(()=>{if(f&&g){let e="super_admin"===g.role?"/admin":"doctor"===g.role?"/dashboard":"/patient";p.push(v||e)}},[f,g,p,v]);let y=async r=>{r.preventDefault(),d(!0),m("");try{let r=await u(e,s);r.error&&m(r.error.message)}catch(e){m(e instanceof Error?e.message:"Login failed")}finally{d(!1)}},j=async()=>{d(!0),m("");try{let e=await b();e.error&&m(e.error.message)}catch(e){m(e instanceof Error?e.message:"Google login failed")}finally{d(!1)}};return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8 p-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-blue-600 mb-2",children:"DentoPro"}),(0,t.jsx)("h2",{className:"text-2xl font-semibold text-gray-900",children:"Sign in to your account"}),(0,t.jsxs)("p",{className:"mt-2 text-sm text-gray-600",children:["Or"," ",(0,t.jsx)(o(),{href:"/auth/register",className:"font-medium text-blue-600 hover:text-blue-500",children:"create a new account"})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[c&&(0,t.jsx)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-md",children:(0,t.jsx)("p",{className:"text-sm text-red-600",children:c})}),(0,t.jsxs)("form",{onSubmit:y,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,t.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>r(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your email"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,t.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:s,onChange:e=>n(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your password"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,t.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900",children:"Remember me"})]}),(0,t.jsx)("div",{className:"text-sm",children:(0,t.jsx)(o(),{href:"/auth/reset-password",className:"font-medium text-blue-600 hover:text-blue-500",children:"Forgot your password?"})})]}),(0,t.jsx)(x,{type:"submit",className:"w-full",disabled:i,children:i?"Signing in...":"Sign in"})]}),(0,t.jsx)("div",{className:"mt-6",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,t.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,t.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,t.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]})}),(0,t.jsx)("div",{className:"mt-6",children:(0,t.jsxs)(x,{type:"button",variant:"outline",className:"w-full",onClick:j,disabled:i,children:[(0,t.jsxs)("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",children:[(0,t.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,t.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,t.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,t.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),i?"Connecting...":"Sign in with Google"]})})]}),(0,t.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-yellow-800 mb-2",children:"Demo Accounts"}),(0,t.jsxs)("div",{className:"text-xs text-yellow-700 space-y-1",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Super Admin:"})," <EMAIL> / admin123"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Doctor:"})," <EMAIL> / doctor123"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Patient:"})," <EMAIL> / patient123"]})]})]}),(0,t.jsx)("div",{className:"text-center text-sm text-gray-500",children:(0,t.jsxs)("p",{children:["By signing in, you agree to our"," ",(0,t.jsx)(o(),{href:"/terms",className:"text-blue-600 hover:text-blue-500",children:"Terms of Service"})," ","and"," ",(0,t.jsx)(o(),{href:"/privacy",className:"text-blue-600 hover:text-blue-500",children:"Privacy Policy"})]})})]})})}}},e=>{e.O(0,[535,347,866,441,964,358],()=>e(e.s=1455)),_N_E=e.O()}]);