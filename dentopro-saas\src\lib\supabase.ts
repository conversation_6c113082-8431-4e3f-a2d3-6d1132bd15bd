// Supabase client configuration for DentoPro SaaS platform
import { createClient } from '@supabase/supabase-js';
import { createBrowserClient } from '@supabase/ssr';

// Database type definitions (will be generated from Supabase)
export interface Database {
  public: {
    Tables: {
      platform_admins: {
        Row: {
          id: string;
          email: string;
          password_hash: string;
          full_name: string;
          role: string;
          permissions: any;
          last_login: string | null;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          password_hash: string;
          full_name: string;
          role?: string;
          permissions?: any;
          last_login?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          password_hash?: string;
          full_name?: string;
          role?: string;
          permissions?: any;
          last_login?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      doctors: {
        Row: {
          id: string;
          email: string;
          password_hash: string;
          full_name: string;
          license_number: string;
          clinic_name: string;
          phone: string | null;
          whatsapp: string | null;
          subscription_plan: string;
          subscription_status: string;
          domain: string | null;
          trial_end_date: string | null;
          approved_by: string | null;
          approved_at: string | null;
          last_login: string | null;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          password_hash: string;
          full_name: string;
          license_number: string;
          clinic_name: string;
          phone?: string | null;
          whatsapp?: string | null;
          subscription_plan?: string;
          subscription_status?: string;
          domain?: string | null;
          trial_end_date?: string | null;
          approved_by?: string | null;
          approved_at?: string | null;
          last_login?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          password_hash?: string;
          full_name?: string;
          license_number?: string;
          clinic_name?: string;
          phone?: string | null;
          whatsapp?: string | null;
          subscription_plan?: string;
          subscription_status?: string;
          domain?: string | null;
          trial_end_date?: string | null;
          approved_by?: string | null;
          approved_at?: string | null;
          last_login?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      patients: {
        Row: {
          id: string;
          doctor_id: string;
          email: string | null;
          full_name: string;
          phone: string;
          date_of_birth: string | null;
          gender: string | null;
          address: string | null;
          emergency_contact_name: string | null;
          emergency_contact_phone: string | null;
          emergency_contact_relationship: string | null;
          medical_history: any;
          dental_history: any;
          allergies: string[] | null;
          current_medications: string[] | null;
          insurance_info: any;
          is_verified: boolean;
          notes: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          doctor_id: string;
          email?: string | null;
          full_name: string;
          phone: string;
          date_of_birth?: string | null;
          gender?: string | null;
          address?: string | null;
          emergency_contact_name?: string | null;
          emergency_contact_phone?: string | null;
          emergency_contact_relationship?: string | null;
          medical_history?: any;
          dental_history?: any;
          allergies?: string[] | null;
          current_medications?: string[] | null;
          insurance_info?: any;
          is_verified?: boolean;
          notes?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          doctor_id?: string;
          email?: string | null;
          full_name?: string;
          phone?: string;
          date_of_birth?: string | null;
          gender?: string | null;
          address?: string | null;
          emergency_contact_name?: string | null;
          emergency_contact_phone?: string | null;
          emergency_contact_relationship?: string | null;
          medical_history?: any;
          dental_history?: any;
          allergies?: string[] | null;
          current_medications?: string[] | null;
          insurance_info?: any;
          is_verified?: boolean;
          notes?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      // Add more table types as needed
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}

// Environment variables validation
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env file.');
}

// Client-side Supabase client (for browser)
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

// Server-side Supabase client with service role (for admin operations)
export const supabaseAdmin = createClient<Database>(
  supabaseUrl,
  supabaseServiceRoleKey || supabaseAnonKey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// Note: Server component client is in supabase-server.ts to avoid Next.js import issues

// Client component client (for Next.js client components)
export const createClientSupabaseClient = () => {
  return createBrowserClient<Database>(supabaseUrl, supabaseAnonKey);
};

// Supabase utilities
export class SupabaseUtils {
  // Get current user with role information
  static async getCurrentUser() {
    const supabase = createClientSupabaseClient();
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      return null;
    }

    // Get user role from custom claims or user metadata
    const role = user.user_metadata?.role || user.app_metadata?.role || 'patient';
    
    return {
      ...user,
      role
    };
  }

  // Check if user has specific role
  static async hasRole(requiredRole: string): Promise<boolean> {
    const user = await this.getCurrentUser();
    return user?.role === requiredRole;
  }

  // Get doctor ID from current user
  static async getCurrentDoctorId(): Promise<string | null> {
    const user = await this.getCurrentUser();
    
    if (user?.role === 'doctor') {
      return user.id;
    }
    
    if (user?.role === 'patient') {
      // For patients, get their doctor_id from the patients table
      const supabase = createClientSupabaseClient();
      const { data: patient } = await supabase
        .from('patients')
        .select('doctor_id')
        .eq('id', user.id)
        .single();
      
      return patient?.doctor_id || null;
    }
    
    return null;
  }

  // Get patient ID from current user
  static async getCurrentPatientId(): Promise<string | null> {
    const user = await this.getCurrentUser();
    return user?.role === 'patient' ? user.id : null;
  }

  // Sign in with email and password
  static async signInWithPassword(email: string, password: string) {
    const supabase = createClientSupabaseClient();
    return await supabase.auth.signInWithPassword({ email, password });
  }

  // Sign in with Google OAuth
  static async signInWithGoogle() {
    const supabase = createClientSupabaseClient();
    return await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`
      }
    });
  }

  // Sign up new user
  static async signUp(email: string, password: string, metadata?: any) {
    const supabase = createClientSupabaseClient();
    return await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata
      }
    });
  }

  // Sign out
  static async signOut() {
    const supabase = createClientSupabaseClient();
    return await supabase.auth.signOut();
  }

  // Update user metadata
  static async updateUserMetadata(metadata: any) {
    const supabase = createClientSupabaseClient();
    return await supabase.auth.updateUser({
      data: metadata
    });
  }

  // Reset password
  static async resetPassword(email: string) {
    const supabase = createClientSupabaseClient();
    return await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`
    });
  }
}

// Database query helpers with RLS
export class SupabaseDatabase {
  private supabase = createClientSupabaseClient();

  // Get data with automatic doctor filtering
  async findMany<T>(
    table: string, 
    filters: Record<string, any> = {},
    options: { 
      select?: string;
      orderBy?: string;
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<T[]> {
    let query = this.supabase.from(table).select(options.select || '*');

    // Apply filters
    Object.entries(filters).forEach(([key, value]) => {
      query = query.eq(key, value);
    });

    // Apply ordering
    if (options.orderBy) {
      const [column, direction] = options.orderBy.split(' ');
      query = query.order(column, { ascending: direction !== 'desc' });
    }

    // Apply pagination
    if (options.limit) {
      query = query.limit(options.limit);
    }
    if (options.offset) {
      query = query.range(options.offset, (options.offset + (options.limit || 10)) - 1);
    }

    const { data, error } = await query;
    
    if (error) {
      console.error('Database query error:', error);
      throw error;
    }

    return data as T[];
  }

  // Find single record
  async findOne<T>(table: string, filters: Record<string, any>): Promise<T | null> {
    let query = this.supabase.from(table).select('*');

    Object.entries(filters).forEach(([key, value]) => {
      query = query.eq(key, value);
    });

    const { data, error } = await query.single();
    
    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Database query error:', error);
      throw error;
    }

    return data as T | null;
  }

  // Insert record
  async insert<T>(table: string, data: any): Promise<T> {
    const { data: result, error } = await this.supabase
      .from(table)
      .insert(data)
      .select()
      .single();

    if (error) {
      console.error('Database insert error:', error);
      throw error;
    }

    return result as T;
  }

  // Update record
  async update<T>(table: string, data: any, filters: Record<string, any>): Promise<T | null> {
    let query = this.supabase.from(table).update(data);

    Object.entries(filters).forEach(([key, value]) => {
      query = query.eq(key, value);
    });

    const { data: result, error } = await query.select().single();

    if (error && error.code !== 'PGRST116') {
      console.error('Database update error:', error);
      throw error;
    }

    return result as T | null;
  }

  // Delete record
  async delete(table: string, filters: Record<string, any>): Promise<void> {
    let query = this.supabase.from(table).delete();

    Object.entries(filters).forEach(([key, value]) => {
      query = query.eq(key, value);
    });

    const { error } = await query;

    if (error) {
      console.error('Database delete error:', error);
      throw error;
    }
  }

  // Count records
  async count(table: string, filters: Record<string, any> = {}): Promise<number> {
    let query = this.supabase.from(table).select('*', { count: 'exact', head: true });

    Object.entries(filters).forEach(([key, value]) => {
      query = query.eq(key, value);
    });

    const { count, error } = await query;

    if (error) {
      console.error('Database count error:', error);
      throw error;
    }

    return count || 0;
  }
}

// Export database instance
export const db = new SupabaseDatabase();

export default supabase;
