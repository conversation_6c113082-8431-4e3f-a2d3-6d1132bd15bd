-- Manual Setup SQL for Supabase
-- Run this in your Supabase SQL Editor if the automated script doesn't work

-- First, create the exec_sql function
CREATE OR REPLACE FUNCTION exec_sql(sql text)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  EXECUTE sql;
  RETURN 'SQL executed successfully';
EXCEPTION
  WHEN OTHERS THEN
    RETURN 'Error: ' || SQLERRM;
END;
$$;

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create platform_admins table
CREATE TABLE IF NOT EXISTS platform_admins (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'super_admin',
    permissions JSONB DEFAULT '{}',
    last_login TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create doctors table
CREATE TABLE IF NOT EXISTS doctors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    license_number VARCHAR(100) UNIQUE NOT NULL,
    clinic_name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    whatsapp VARCHAR(20),
    subscription_plan VARCHAR(50) NOT NULL DEFAULT 'trial',
    subscription_status VARCHAR(50) NOT NULL DEFAULT 'trial',
    domain VARCHAR(255),
    trial_end_date TIMESTAMP WITH TIME ZONE,
    approved_by UUID REFERENCES platform_admins(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    last_login TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create patients table
CREATE TABLE IF NOT EXISTS patients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    email VARCHAR(255),
    full_name VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    date_of_birth DATE,
    gender VARCHAR(10),
    address TEXT,
    emergency_contact_name VARCHAR(255),
    emergency_contact_phone VARCHAR(20),
    emergency_contact_relationship VARCHAR(100),
    medical_history JSONB DEFAULT '{}',
    dental_history JSONB DEFAULT '{}',
    allergies TEXT[],
    current_medications TEXT[],
    insurance_info JSONB DEFAULT '{}',
    is_verified BOOLEAN DEFAULT false,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(doctor_id, phone)
);

-- Create appointments table
CREATE TABLE IF NOT EXISTS appointments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    patient_id UUID NOT NULL REFERENCES patients(id) ON DELETE CASCADE,
    appointment_date DATE NOT NULL,
    appointment_time TIME NOT NULL,
    duration INTEGER DEFAULT 30,
    appointment_type VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'scheduled',
    notes TEXT,
    reminder_sent BOOLEAN DEFAULT false,
    source VARCHAR(50) DEFAULT 'doctor',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create treatments table
CREATE TABLE IF NOT EXISTS treatments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    doctor_id UUID NOT NULL REFERENCES doctors(id) ON DELETE CASCADE,
    patient_id UUID NOT NULL REFERENCES patients(id) ON DELETE CASCADE,
    appointment_id UUID REFERENCES appointments(id) ON DELETE SET NULL,
    procedure_name VARCHAR(255) NOT NULL,
    tooth_number VARCHAR(10),
    surface VARCHAR(50),
    diagnosis TEXT,
    treatment_plan TEXT,
    notes TEXT,
    cost DECIMAL(10,2),
    materials_used JSONB DEFAULT '[]',
    before_images TEXT[],
    after_images TEXT[],
    pain_score INTEGER CHECK (pain_score >= 0 AND pain_score <= 10),
    follow_up_required BOOLEAN DEFAULT false,
    follow_up_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE platform_admins ENABLE ROW LEVEL SECURITY;
ALTER TABLE doctors ENABLE ROW LEVEL SECURITY;
ALTER TABLE patients ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Super admins can access all platform_admins" ON platform_admins
    FOR ALL TO authenticated
    USING (auth.jwt() ->> 'role' = 'super_admin');

CREATE POLICY "Super admins can access all doctors" ON doctors
    FOR ALL TO authenticated
    USING (auth.jwt() ->> 'role' = 'super_admin');

CREATE POLICY "Doctors can access their own record" ON doctors
    FOR ALL TO authenticated
    USING (auth.jwt() ->> 'role' = 'doctor' AND id = auth.uid());

CREATE POLICY "Super admins can access all patients" ON patients
    FOR ALL TO authenticated
    USING (auth.jwt() ->> 'role' = 'super_admin');

CREATE POLICY "Doctors can access their own patients" ON patients
    FOR ALL TO authenticated
    USING (auth.jwt() ->> 'role' = 'doctor' AND doctor_id = auth.uid());

CREATE POLICY "Patients can view their own record" ON patients
    FOR SELECT TO authenticated
    USING (auth.jwt() ->> 'role' = 'patient' AND id = auth.uid());

-- Insert demo admin user into platform_admins table
INSERT INTO platform_admins (
    id,
    email,
    password_hash,
    full_name,
    role,
    is_active
) VALUES (
    'a1b2c3d4-e5f6-7890-1234-567890abcdef'::uuid,
    '<EMAIL>',
    '$2b$12$demo.hash.value',
    'Super Admin',
    'super_admin',
    true
) ON CONFLICT (email) DO NOTHING;

-- Insert demo doctor user into doctors table
INSERT INTO doctors (
    id,
    email,
    password_hash,
    full_name,
    license_number,
    clinic_name,
    phone,
    subscription_plan,
    subscription_status,
    is_active
) VALUES (
    'b2c3d4e5-f6g7-8901-2345-678901bcdefg'::uuid,
    '<EMAIL>',
    '$2b$12$demo.hash.value',
    'Dr. Demo Doctor',
    'LIC123456',
    'Demo Dental Clinic',
    '+**********',
    'premium',
    'active',
    true
) ON CONFLICT (email) DO NOTHING;

-- Insert some demo patients for the doctor
INSERT INTO patients (
    id,
    doctor_id,
    full_name,
    phone,
    email,
    date_of_birth,
    gender,
    address,
    medical_history,
    dental_history,
    is_verified
) VALUES
(
    'c3d4e5f6-g7h8-9012-3456-789012cdefgh'::uuid,
    'b2c3d4e5-f6g7-8901-2345-678901bcdefg'::uuid,
    'John Smith',
    '+**********',
    '<EMAIL>',
    '1985-06-15',
    'male',
    '123 Main St, City, State 12345',
    '{"conditions": ["hypertension"], "medications": ["lisinopril"]}',
    '{"last_cleaning": "2024-06-01", "cavities": 2}',
    true
),
(
    'd4e5f6g7-h8i9-0123-4567-890123defghi'::uuid,
    'b2c3d4e5-f6g7-8901-2345-678901bcdefg'::uuid,
    'Sarah Johnson',
    '+**********',
    '<EMAIL>',
    '1990-03-22',
    'female',
    '456 Oak Ave, City, State 12345',
    '{"conditions": [], "medications": []}',
    '{"last_cleaning": "2024-05-15", "orthodontics": "completed"}',
    true
) ON CONFLICT (doctor_id, phone) DO NOTHING;

-- Insert some demo appointments
INSERT INTO appointments (
    id,
    doctor_id,
    patient_id,
    appointment_date,
    appointment_time,
    appointment_type,
    status,
    notes
) VALUES
(
    'e5f6g7h8-i9j0-1234-5678-901234efghij'::uuid,
    'b2c3d4e5-f6g7-8901-2345-678901bcdefg'::uuid,
    'c3d4e5f6-g7h8-9012-3456-789012cdefgh'::uuid,
    CURRENT_DATE + INTERVAL '1 day',
    '10:00:00',
    'cleaning',
    'scheduled',
    'Regular cleaning and checkup'
),
(
    'f6g7h8i9-j0k1-2345-6789-012345fghijk'::uuid,
    'b2c3d4e5-f6g7-8901-2345-678901bcdefg'::uuid,
    'd4e5f6g7-h8i9-0123-4567-890123defghi'::uuid,
    CURRENT_DATE + INTERVAL '3 days',
    '14:30:00',
    'consultation',
    'scheduled',
    'Consultation for wisdom tooth removal'
) ON CONFLICT DO NOTHING;

SELECT 'Complete DentoPro setup with demo data completed successfully!' as status;
