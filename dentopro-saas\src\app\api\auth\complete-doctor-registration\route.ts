import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase-server';
import { stripe, SUBSCRIPTION_PLANS, calculateTrialEndDate } from '@/lib/stripe';

export async function POST(request: NextRequest) {
  try {
    const {
      userId,
      fullName,
      medicalLicense,
      clinicName,
      phoneNumber,
      whatsappNumber,
      selectedPlan
    } = await request.json();

    if (!userId || !fullName || !medicalLicense || !clinicName || !phoneNumber || !selectedPlan) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const supabase = createServerSupabaseClient();

    // Get user email from auth
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Check if doctor already exists
    const { data: existingDoctor } = await supabase
      .from('doctors')
      .select('id')
      .eq('email', user.email)
      .single();

    if (existingDoctor) {
      return NextResponse.json(
        { error: 'Doctor profile already exists' },
        { status: 400 }
      );
    }

    // Create Stripe customer
    const customer = await stripe.customers.create({
      email: user.email,
      name: fullName,
      metadata: {
        userId: userId,
        clinicName: clinicName,
        medicalLicense: medicalLicense
      }
    });

    // Calculate trial dates
    const trialStart = new Date();
    const trialEnd = calculateTrialEndDate(trialStart);

    // Create doctor profile
    const { data: doctor, error: doctorError } = await supabase
      .from('doctors')
      .insert({
        id: userId,
        email: user.email,
        full_name: fullName,
        medical_license: medicalLicense,
        clinic_name: clinicName,
        phone_number: phoneNumber,
        whatsapp_number: whatsappNumber || null,
        stripe_customer_id: customer.id,
        subscription_status: 'trial',
        trial_start_date: trialStart.toISOString(),
        trial_end_date: trialEnd.toISOString(),
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (doctorError) {
      console.error('Doctor creation error:', doctorError);
      return NextResponse.json(
        { error: 'Failed to create doctor profile' },
        { status: 500 }
      );
    }

    // Create subscription record
    const selectedPlanConfig = Object.values(SUBSCRIPTION_PLANS).find(p => p.id === selectedPlan);
    
    if (!selectedPlanConfig) {
      return NextResponse.json(
        { error: 'Invalid subscription plan' },
        { status: 400 }
      );
    }

    const { data: subscription, error: subscriptionError } = await supabase
      .from('subscriptions')
      .insert({
        doctor_id: userId,
        plan_id: selectedPlan,
        status: selectedPlan === 'plan_trial' ? 'trial' : 'pending',
        trial_start: trialStart.toISOString(),
        trial_end: trialEnd.toISOString(),
        current_period_start: trialStart.toISOString(),
        current_period_end: trialEnd.toISOString(),
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (subscriptionError) {
      console.error('Subscription creation error:', subscriptionError);
      return NextResponse.json(
        { error: 'Failed to create subscription' },
        { status: 500 }
      );
    }

    // Create default clinic settings
    await supabase
      .from('clinic_settings')
      .insert({
        doctor_id: userId,
        clinic_name: clinicName,
        phone_number: phoneNumber,
        whatsapp_number: whatsappNumber || null,
        working_hours: {
          monday: { start: '09:00', end: '17:00', enabled: true },
          tuesday: { start: '09:00', end: '17:00', enabled: true },
          wednesday: { start: '09:00', end: '17:00', enabled: true },
          thursday: { start: '09:00', end: '17:00', enabled: true },
          friday: { start: '09:00', end: '17:00', enabled: true },
          saturday: { start: '09:00', end: '13:00', enabled: true },
          sunday: { start: '09:00', end: '13:00', enabled: false }
        },
        appointment_duration: 30,
        buffer_time: 15,
        advance_booking_days: 30,
        timezone: 'UTC',
        language: 'en',
        currency: 'USD',
        created_at: new Date().toISOString()
      });

    // Log the registration
    await supabase
      .from('audit_logs')
      .insert({
        user_id: userId,
        action: 'doctor_registration_completed',
        resource_type: 'doctor',
        resource_id: userId,
        details: {
          clinicName: clinicName,
          selectedPlan: selectedPlan,
          trialEndDate: trialEnd.toISOString()
        },
        ip_address: request.headers.get('x-forwarded-for') || 'unknown',
        user_agent: request.headers.get('user-agent') || 'unknown',
        created_at: new Date().toISOString()
      });

    return NextResponse.json({
      success: true,
      doctorId: userId,
      subscriptionId: subscription.id,
      trialEndDate: trialEnd.toISOString(),
      message: 'Doctor registration completed successfully'
    });

  } catch (error) {
    console.error('Doctor registration error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
