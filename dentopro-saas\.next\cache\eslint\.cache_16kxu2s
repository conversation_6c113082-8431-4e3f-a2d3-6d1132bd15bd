[{"D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\app\\auth\\callback\\route.ts": "1", "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\app\\auth\\login\\page.tsx": "2", "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\app\\layout.tsx": "3", "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\app\\page.tsx": "4", "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\components\\ui\\button.tsx": "5", "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\contexts\\ThemeContext.tsx": "6", "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\hooks\\useAuth.tsx": "7", "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\hooks\\useLocalStorage.ts": "8", "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\hooks\\useRealtimeSubscription.ts": "9", "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\lib\\constants.ts": "10", "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\lib\\database-testing.ts": "11", "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\lib\\database.ts": "12", "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\lib\\encryption.ts": "13", "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\lib\\supabase-server.ts": "14", "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\lib\\supabase-storage.ts": "15", "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\lib\\supabase.ts": "16", "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\lib\\utils.ts": "17", "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\middleware.ts": "18", "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\types\\index.ts": "19"}, {"size": 4068, "mtime": 1753104771448, "results": "20", "hashOfConfig": "21"}, {"size": 8319, "mtime": 1753103749079, "results": "22", "hashOfConfig": "21"}, {"size": 1562, "mtime": 1753105232381, "results": "23", "hashOfConfig": "21"}, {"size": 7921, "mtime": 1753111140947, "results": "24", "hashOfConfig": "21"}, {"size": 1845, "mtime": 1753101217287, "results": "25", "hashOfConfig": "21"}, {"size": 1633, "mtime": 1753101585683, "results": "26", "hashOfConfig": "21"}, {"size": 7197, "mtime": 1753103451699, "results": "27", "hashOfConfig": "21"}, {"size": 1509, "mtime": 1753101573437, "results": "28", "hashOfConfig": "21"}, {"size": 7945, "mtime": 1753104866742, "results": "29", "hashOfConfig": "21"}, {"size": 9650, "mtime": 1753101108323, "results": "30", "hashOfConfig": "21"}, {"size": 16190, "mtime": 1753102709817, "results": "31", "hashOfConfig": "21"}, {"size": 8353, "mtime": 1753104825916, "results": "32", "hashOfConfig": "21"}, {"size": 12323, "mtime": 1753102544171, "results": "33", "hashOfConfig": "21"}, {"size": 794, "mtime": 1753105147041, "results": "34", "hashOfConfig": "21"}, {"size": 10385, "mtime": 1753104617715, "results": "35", "hashOfConfig": "21"}, {"size": 13166, "mtime": 1753105821279, "results": "36", "hashOfConfig": "21"}, {"size": 10796, "mtime": 1753101155125, "results": "37", "hashOfConfig": "21"}, {"size": 7819, "mtime": 1753104746950, "results": "38", "hashOfConfig": "21"}, {"size": 10163, "mtime": 1753101057142, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1lk4bmj", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\app\\auth\\callback\\route.ts", ["97", "98"], [], "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\app\\auth\\login\\page.tsx", [], [], "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\app\\layout.tsx", [], [], "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\app\\page.tsx", [], [], "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\components\\ui\\button.tsx", [], [], "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\contexts\\ThemeContext.tsx", [], [], "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\hooks\\useAuth.tsx", ["99", "100", "101", "102", "103", "104", "105", "106", "107", "108"], [], "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\hooks\\useLocalStorage.ts", ["109"], [], "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\hooks\\useRealtimeSubscription.ts", ["110", "111", "112", "113", "114", "115", "116", "117", "118", "119", "120", "121", "122", "123", "124", "125", "126", "127", "128", "129"], [], "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\lib\\constants.ts", [], [], "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\lib\\database-testing.ts", ["130", "131", "132", "133", "134", "135", "136", "137", "138", "139", "140", "141", "142", "143", "144", "145", "146", "147", "148", "149", "150", "151", "152", "153", "154", "155", "156", "157"], [], "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\lib\\database.ts", ["158", "159", "160", "161", "162", "163", "164", "165", "166", "167", "168", "169", "170", "171", "172", "173", "174"], [], "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\lib\\encryption.ts", ["175", "176", "177", "178", "179", "180", "181", "182", "183", "184", "185", "186", "187", "188", "189"], [], "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\lib\\supabase-server.ts", ["190", "191"], [], "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\lib\\supabase-storage.ts", ["192", "193", "194", "195", "196", "197", "198"], [], "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\lib\\supabase.ts", ["199", "200", "201", "202", "203", "204", "205", "206", "207", "208", "209", "210", "211", "212", "213", "214", "215", "216", "217", "218", "219"], [], "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\lib\\utils.ts", ["220", "221", "222", "223", "224", "225", "226", "227"], [], "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\middleware.ts", ["228"], [], "D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\types\\index.ts", ["229", "230", "231"], [], {"ruleId": "232", "severity": 1, "message": "233", "line": 79, "column": 44, "nodeType": "234", "messageId": "235", "endLine": 79, "endColumn": 47, "suggestions": "236"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 79, "column": 55, "nodeType": "234", "messageId": "235", "endLine": 79, "endColumn": 58, "suggestions": "237"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 12, "column": 55, "nodeType": "234", "messageId": "235", "endLine": 12, "endColumn": 58, "suggestions": "238"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 13, "column": 34, "nodeType": "234", "messageId": "235", "endLine": 13, "endColumn": 37, "suggestions": "239"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 15, "column": 24, "nodeType": "234", "messageId": "235", "endLine": 15, "endColumn": 27, "suggestions": "240"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 15, "column": 40, "nodeType": "234", "messageId": "235", "endLine": 15, "endColumn": 43, "suggestions": "241"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 16, "column": 45, "nodeType": "234", "messageId": "235", "endLine": 16, "endColumn": 48, "suggestions": "242"}, {"ruleId": "243", "severity": 1, "message": "244", "line": 63, "column": 6, "nodeType": "245", "endLine": 63, "endColumn": 21, "suggestions": "246"}, {"ruleId": "247", "severity": 1, "message": "248", "line": 118, "column": 14, "nodeType": null, "messageId": "249", "endLine": 118, "endColumn": 19}, {"ruleId": "247", "severity": 1, "message": "248", "line": 133, "column": 14, "nodeType": null, "messageId": "249", "endLine": 133, "endColumn": 19}, {"ruleId": "247", "severity": 1, "message": "248", "line": 148, "column": 14, "nodeType": null, "messageId": "249", "endLine": 148, "endColumn": 19}, {"ruleId": "232", "severity": 1, "message": "233", "line": 207, "column": 16, "nodeType": "234", "messageId": "235", "endLine": 207, "endColumn": 19, "suggestions": "250"}, {"ruleId": "247", "severity": 1, "message": "251", "line": 1, "column": 20, "nodeType": null, "messageId": "249", "endLine": 1, "endColumn": 29}, {"ruleId": "232", "severity": 1, "message": "233", "line": 23, "column": 68, "nodeType": "234", "messageId": "235", "endLine": 23, "endColumn": 71, "suggestions": "252"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 23, "column": 76, "nodeType": "234", "messageId": "235", "endLine": 23, "endColumn": 79, "suggestions": "253"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 121, "column": 55, "nodeType": "234", "messageId": "235", "endLine": 121, "endColumn": 58, "suggestions": "254"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 121, "column": 83, "nodeType": "234", "messageId": "235", "endLine": 121, "endColumn": 86, "suggestions": "255"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 131, "column": 22, "nodeType": "234", "messageId": "235", "endLine": 131, "endColumn": 25, "suggestions": "256"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 131, "column": 50, "nodeType": "234", "messageId": "235", "endLine": 131, "endColumn": 53, "suggestions": "257"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 139, "column": 22, "nodeType": "234", "messageId": "235", "endLine": 139, "endColumn": 25, "suggestions": "258"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 139, "column": 50, "nodeType": "234", "messageId": "235", "endLine": 139, "endColumn": 53, "suggestions": "259"}, {"ruleId": "247", "severity": 1, "message": "260", "line": 177, "column": 9, "nodeType": null, "messageId": "249", "endLine": 177, "endColumn": 27}, {"ruleId": "232", "severity": 1, "message": "233", "line": 181, "column": 31, "nodeType": "234", "messageId": "235", "endLine": 181, "endColumn": 34, "suggestions": "261"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 234, "column": 34, "nodeType": "234", "messageId": "235", "endLine": 234, "endColumn": 37, "suggestions": "262"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 242, "column": 34, "nodeType": "234", "messageId": "235", "endLine": 242, "endColumn": 37, "suggestions": "263"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 256, "column": 34, "nodeType": "234", "messageId": "235", "endLine": 256, "endColumn": 37, "suggestions": "264"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 264, "column": 34, "nodeType": "234", "messageId": "235", "endLine": 264, "endColumn": 37, "suggestions": "265"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 271, "column": 34, "nodeType": "234", "messageId": "235", "endLine": 271, "endColumn": 37, "suggestions": "266"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 280, "column": 34, "nodeType": "234", "messageId": "235", "endLine": 280, "endColumn": 37, "suggestions": "267"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 289, "column": 34, "nodeType": "234", "messageId": "235", "endLine": 289, "endColumn": 37, "suggestions": "268"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 300, "column": 34, "nodeType": "234", "messageId": "235", "endLine": 300, "endColumn": 37, "suggestions": "269"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 308, "column": 60, "nodeType": "234", "messageId": "235", "endLine": 308, "endColumn": 63, "suggestions": "270"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 308, "column": 68, "nodeType": "234", "messageId": "235", "endLine": 308, "endColumn": 71, "suggestions": "271"}, {"ruleId": "247", "severity": 1, "message": "272", "line": 2, "column": 16, "nodeType": null, "messageId": "249", "endLine": 2, "endColumn": 26}, {"ruleId": "232", "severity": 1, "message": "233", "line": 15, "column": 44, "nodeType": "234", "messageId": "235", "endLine": 15, "endColumn": 47, "suggestions": "273"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 15, "column": 56, "nodeType": "234", "messageId": "235", "endLine": 15, "endColumn": 59, "suggestions": "274"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 30, "column": 63, "nodeType": "234", "messageId": "235", "endLine": 30, "endColumn": 66, "suggestions": "275"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 30, "column": 75, "nodeType": "234", "messageId": "235", "endLine": 30, "endColumn": 78, "suggestions": "276"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 44, "column": 86, "nodeType": "234", "messageId": "235", "endLine": 44, "endColumn": 89, "suggestions": "277"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 44, "column": 98, "nodeType": "234", "messageId": "235", "endLine": 44, "endColumn": 101, "suggestions": "278"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 61, "column": 108, "nodeType": "234", "messageId": "235", "endLine": 61, "endColumn": 111, "suggestions": "279"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 61, "column": 120, "nodeType": "234", "messageId": "235", "endLine": 61, "endColumn": 123, "suggestions": "280"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 76, "column": 64, "nodeType": "234", "messageId": "235", "endLine": 76, "endColumn": 67, "suggestions": "281"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 76, "column": 76, "nodeType": "234", "messageId": "235", "endLine": 76, "endColumn": 79, "suggestions": "282"}, {"ruleId": "247", "severity": 1, "message": "248", "line": 114, "column": 14, "nodeType": null, "messageId": "249", "endLine": 114, "endColumn": 19}, {"ruleId": "283", "severity": 1, "message": "284", "line": 127, "column": 16, "nodeType": "285", "messageId": "286", "endLine": 127, "endColumn": 29}, {"ruleId": "283", "severity": 1, "message": "284", "line": 128, "column": 18, "nodeType": "285", "messageId": "286", "endLine": 128, "endColumn": 33}, {"ruleId": "232", "severity": 1, "message": "233", "line": 160, "column": 44, "nodeType": "234", "messageId": "235", "endLine": 160, "endColumn": 47, "suggestions": "287"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 188, "column": 61, "nodeType": "234", "messageId": "235", "endLine": 188, "endColumn": 64, "suggestions": "288"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 195, "column": 13, "nodeType": "234", "messageId": "235", "endLine": 195, "endColumn": 16, "suggestions": "289"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 196, "column": 15, "nodeType": "234", "messageId": "235", "endLine": 196, "endColumn": 18, "suggestions": "290"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 197, "column": 19, "nodeType": "234", "messageId": "235", "endLine": 197, "endColumn": 22, "suggestions": "291"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 198, "column": 17, "nodeType": "234", "messageId": "235", "endLine": 198, "endColumn": 20, "suggestions": "292"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 199, "column": 16, "nodeType": "234", "messageId": "235", "endLine": 199, "endColumn": 19, "suggestions": "293"}, {"ruleId": "247", "severity": 1, "message": "294", "line": 252, "column": 13, "nodeType": null, "messageId": "249", "endLine": 252, "endColumn": 21}, {"ruleId": "247", "severity": 1, "message": "295", "line": 253, "column": 13, "nodeType": null, "messageId": "249", "endLine": 253, "endColumn": 21}, {"ruleId": "247", "severity": 1, "message": "296", "line": 275, "column": 23, "nodeType": null, "messageId": "249", "endLine": 275, "endColumn": 31}, {"ruleId": "247", "severity": 1, "message": "297", "line": 275, "column": 33, "nodeType": null, "messageId": "249", "endLine": 275, "endColumn": 45}, {"ruleId": "247", "severity": 1, "message": "248", "line": 338, "column": 16, "nodeType": null, "messageId": "249", "endLine": 338, "endColumn": 21}, {"ruleId": "232", "severity": 1, "message": "233", "line": 352, "column": 39, "nodeType": "234", "messageId": "235", "endLine": 352, "endColumn": 42, "suggestions": "298"}, {"ruleId": "299", "severity": 1, "message": "300", "line": 459, "column": 1, "nodeType": "301", "endLine": 464, "endColumn": 3}, {"ruleId": "247", "severity": 1, "message": "302", "line": 18, "column": 11, "nodeType": null, "messageId": "249", "endLine": 18, "endColumn": 23}, {"ruleId": "232", "severity": 1, "message": "233", "line": 20, "column": 12, "nodeType": "234", "messageId": "235", "endLine": 20, "endColumn": 15, "suggestions": "303"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 74, "column": 56, "nodeType": "234", "messageId": "235", "endLine": 74, "endColumn": 59, "suggestions": "304"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 76, "column": 12, "nodeType": "234", "messageId": "235", "endLine": 76, "endColumn": 15, "suggestions": "305"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 100, "column": 66, "nodeType": "234", "messageId": "235", "endLine": 100, "endColumn": 69, "suggestions": "306"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 103, "column": 12, "nodeType": "234", "messageId": "235", "endLine": 103, "endColumn": 15, "suggestions": "307"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 185, "column": 44, "nodeType": "234", "messageId": "235", "endLine": 185, "endColumn": 47, "suggestions": "308"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 185, "column": 91, "nodeType": "234", "messageId": "235", "endLine": 185, "endColumn": 94, "suggestions": "309"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 198, "column": 45, "nodeType": "234", "messageId": "235", "endLine": 198, "endColumn": 48, "suggestions": "310"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 200, "column": 32, "nodeType": "234", "messageId": "235", "endLine": 200, "endColumn": 35, "suggestions": "311"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 229, "column": 43, "nodeType": "234", "messageId": "235", "endLine": 229, "endColumn": 46, "suggestions": "312"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 231, "column": 26, "nodeType": "234", "messageId": "235", "endLine": 231, "endColumn": 29, "suggestions": "313"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 246, "column": 43, "nodeType": "234", "messageId": "235", "endLine": 246, "endColumn": 46, "suggestions": "314"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 248, "column": 26, "nodeType": "234", "messageId": "235", "endLine": 248, "endColumn": 29, "suggestions": "315"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 249, "column": 32, "nodeType": "234", "messageId": "235", "endLine": 249, "endColumn": 35, "suggestions": "316"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 271, "column": 32, "nodeType": "234", "messageId": "235", "endLine": 271, "endColumn": 35, "suggestions": "317"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 287, "column": 32, "nodeType": "234", "messageId": "235", "endLine": 287, "endColumn": 35, "suggestions": "318"}, {"ruleId": "247", "severity": 1, "message": "319", "line": 58, "column": 11, "nodeType": null, "messageId": "249", "endLine": 58, "endColumn": 13}, {"ruleId": "232", "severity": 1, "message": "233", "line": 95, "column": 44, "nodeType": "234", "messageId": "235", "endLine": 95, "endColumn": 47, "suggestions": "320"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 114, "column": 53, "nodeType": "234", "messageId": "235", "endLine": 114, "endColumn": 56, "suggestions": "321"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 147, "column": 41, "nodeType": "234", "messageId": "235", "endLine": 147, "endColumn": 44, "suggestions": "322"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 152, "column": 59, "nodeType": "234", "messageId": "235", "endLine": 152, "endColumn": 62, "suggestions": "323"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 191, "column": 42, "nodeType": "234", "messageId": "235", "endLine": 191, "endColumn": 45, "suggestions": "324"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 191, "column": 48, "nodeType": "234", "messageId": "235", "endLine": 191, "endColumn": 51, "suggestions": "325"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 222, "column": 44, "nodeType": "234", "messageId": "235", "endLine": 222, "endColumn": 47, "suggestions": "326"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 222, "column": 50, "nodeType": "234", "messageId": "235", "endLine": 222, "endColumn": 53, "suggestions": "327"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 258, "column": 46, "nodeType": "234", "messageId": "235", "endLine": 258, "endColumn": 49, "suggestions": "328"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 258, "column": 52, "nodeType": "234", "messageId": "235", "endLine": 258, "endColumn": 55, "suggestions": "329"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 277, "column": 46, "nodeType": "234", "messageId": "235", "endLine": 277, "endColumn": 49, "suggestions": "330"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 277, "column": 52, "nodeType": "234", "messageId": "235", "endLine": 277, "endColumn": 55, "suggestions": "331"}, {"ruleId": "247", "severity": 1, "message": "332", "line": 327, "column": 11, "nodeType": null, "messageId": "249", "endLine": 327, "endColumn": 19}, {"ruleId": "299", "severity": 1, "message": "300", "line": 388, "column": 1, "nodeType": "301", "endLine": 401, "endColumn": 3}, {"ruleId": "232", "severity": 1, "message": "233", "line": 18, "column": 30, "nodeType": "234", "messageId": "235", "endLine": 18, "endColumn": 33, "suggestions": "333"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 19, "column": 59, "nodeType": "234", "messageId": "235", "endLine": 19, "endColumn": 62, "suggestions": "334"}, {"ruleId": "247", "severity": 1, "message": "335", "line": 3, "column": 10, "nodeType": null, "messageId": "249", "endLine": 3, "endColumn": 17}, {"ruleId": "232", "severity": 1, "message": "233", "line": 39, "column": 29, "nodeType": "234", "messageId": "235", "endLine": 39, "endColumn": 32, "suggestions": "336"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 53, "column": 44, "nodeType": "234", "messageId": "235", "endLine": 53, "endColumn": 47, "suggestions": "337"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 60, "column": 52, "nodeType": "234", "messageId": "235", "endLine": 60, "endColumn": 55, "suggestions": "338"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 66, "column": 50, "nodeType": "234", "messageId": "235", "endLine": 66, "endColumn": 53, "suggestions": "339"}, {"ruleId": "247", "severity": 1, "message": "340", "line": 398, "column": 40, "nodeType": null, "messageId": "249", "endLine": 398, "endColumn": 45}, {"ruleId": "247", "severity": 1, "message": "341", "line": 398, "column": 61, "nodeType": null, "messageId": "249", "endLine": 398, "endColumn": 67}, {"ruleId": "232", "severity": 1, "message": "233", "line": 16, "column": 24, "nodeType": "234", "messageId": "235", "endLine": 16, "endColumn": 27, "suggestions": "342"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 28, "column": 25, "nodeType": "234", "messageId": "235", "endLine": 28, "endColumn": 28, "suggestions": "343"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 40, "column": 25, "nodeType": "234", "messageId": "235", "endLine": 40, "endColumn": 28, "suggestions": "344"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 122, "column": 28, "nodeType": "234", "messageId": "235", "endLine": 122, "endColumn": 31, "suggestions": "345"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 123, "column": 27, "nodeType": "234", "messageId": "235", "endLine": 123, "endColumn": 30, "suggestions": "346"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 126, "column": 27, "nodeType": "234", "messageId": "235", "endLine": 126, "endColumn": 30, "suggestions": "347"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 144, "column": 29, "nodeType": "234", "messageId": "235", "endLine": 144, "endColumn": 32, "suggestions": "348"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 145, "column": 28, "nodeType": "234", "messageId": "235", "endLine": 145, "endColumn": 31, "suggestions": "349"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 148, "column": 28, "nodeType": "234", "messageId": "235", "endLine": 148, "endColumn": 31, "suggestions": "350"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 166, "column": 29, "nodeType": "234", "messageId": "235", "endLine": 166, "endColumn": 32, "suggestions": "351"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 167, "column": 28, "nodeType": "234", "messageId": "235", "endLine": 167, "endColumn": 31, "suggestions": "352"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 170, "column": 28, "nodeType": "234", "messageId": "235", "endLine": 170, "endColumn": 31, "suggestions": "353"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 301, "column": 67, "nodeType": "234", "messageId": "235", "endLine": 301, "endColumn": 70, "suggestions": "354"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 319, "column": 45, "nodeType": "234", "messageId": "235", "endLine": 319, "endColumn": 48, "suggestions": "355"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 342, "column": 29, "nodeType": "234", "messageId": "235", "endLine": 342, "endColumn": 32, "suggestions": "356"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 382, "column": 59, "nodeType": "234", "messageId": "235", "endLine": 382, "endColumn": 62, "suggestions": "357"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 400, "column": 40, "nodeType": "234", "messageId": "235", "endLine": 400, "endColumn": 43, "suggestions": "358"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 416, "column": 40, "nodeType": "234", "messageId": "235", "endLine": 416, "endColumn": 43, "suggestions": "359"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 416, "column": 69, "nodeType": "234", "messageId": "235", "endLine": 416, "endColumn": 72, "suggestions": "360"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 434, "column": 55, "nodeType": "234", "messageId": "235", "endLine": 434, "endColumn": 58, "suggestions": "361"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 450, "column": 54, "nodeType": "234", "messageId": "235", "endLine": 450, "endColumn": 57, "suggestions": "362"}, {"ruleId": "247", "severity": 1, "message": "363", "line": 3, "column": 32, "nodeType": null, "messageId": "249", "endLine": 3, "endColumn": 51}, {"ruleId": "247", "severity": 1, "message": "364", "line": 4, "column": 20, "nodeType": null, "messageId": "249", "endLine": 4, "endColumn": 28}, {"ruleId": "232", "severity": 1, "message": "233", "line": 222, "column": 29, "nodeType": "234", "messageId": "235", "endLine": 222, "endColumn": 32, "suggestions": "365"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 257, "column": 39, "nodeType": "234", "messageId": "235", "endLine": 257, "endColumn": 42, "suggestions": "366"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 348, "column": 46, "nodeType": "234", "messageId": "235", "endLine": 348, "endColumn": 49, "suggestions": "367"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 348, "column": 56, "nodeType": "234", "messageId": "235", "endLine": 348, "endColumn": 59, "suggestions": "368"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 360, "column": 46, "nodeType": "234", "messageId": "235", "endLine": 360, "endColumn": 49, "suggestions": "369"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 360, "column": 56, "nodeType": "234", "messageId": "235", "endLine": 360, "endColumn": 59, "suggestions": "370"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 64, "column": 38, "nodeType": "234", "messageId": "235", "endLine": 64, "endColumn": 41, "suggestions": "371"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 411, "column": 29, "nodeType": "234", "messageId": "235", "endLine": 411, "endColumn": 32, "suggestions": "372"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 431, "column": 29, "nodeType": "234", "messageId": "235", "endLine": 431, "endColumn": 32, "suggestions": "373"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 454, "column": 34, "nodeType": "234", "messageId": "235", "endLine": 454, "endColumn": 37, "suggestions": "374"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["375", "376"], ["377", "378"], ["379", "380"], ["381", "382"], ["383", "384"], ["385", "386"], ["387", "388"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleAuthStateChange'. Either include it or remove the dependency array.", "ArrayExpression", ["389"], "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", ["390", "391"], "'useEffect' is defined but never used.", ["392", "393"], ["394", "395"], ["396", "397"], ["398", "399"], ["400", "401"], ["402", "403"], ["404", "405"], ["406", "407"], "'subscriptionFilter' is assigned a value but never used.", ["408", "409"], ["410", "411"], ["412", "413"], ["414", "415"], ["416", "417"], ["418", "419"], ["420", "421"], ["422", "423"], ["424", "425"], ["426", "427"], ["428", "429"], "'PoolClient' is defined but never used.", ["430", "431"], ["432", "433"], ["434", "435"], ["436", "437"], ["438", "439"], ["440", "441"], ["442", "443"], ["444", "445"], ["446", "447"], ["448", "449"], "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["450", "451"], ["452", "453"], ["454", "455"], ["456", "457"], ["458", "459"], ["460", "461"], ["462", "463"], "'patient1' is assigned a value but never used.", "'patient2' is assigned a value but never used.", "'patients' is assigned a value but never used.", "'appointments' is assigned a value but never used.", ["464", "465"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'QueryOptions' is defined but never used.", ["466", "467"], ["468", "469"], ["470", "471"], ["472", "473"], ["474", "475"], ["476", "477"], ["478", "479"], ["480", "481"], ["482", "483"], ["484", "485"], ["486", "487"], ["488", "489"], ["490", "491"], ["492", "493"], ["494", "495"], ["496", "497"], "'iv' is assigned a value but never used.", ["498", "499"], ["500", "501"], ["502", "503"], ["504", "505"], ["506", "507"], ["508", "509"], ["510", "511"], ["512", "513"], ["514", "515"], ["516", "517"], ["518", "519"], ["520", "521"], "'ivBuffer' is assigned a value but never used.", ["522", "523"], ["524", "525"], "'useAuth' is defined but never used.", ["526", "527"], ["528", "529"], ["530", "531"], ["532", "533"], "'width' is assigned a value but never used.", "'height' is assigned a value but never used.", ["534", "535"], ["536", "537"], ["538", "539"], ["540", "541"], ["542", "543"], ["544", "545"], ["546", "547"], ["548", "549"], ["550", "551"], ["552", "553"], ["554", "555"], ["556", "557"], ["558", "559"], ["560", "561"], ["562", "563"], ["564", "565"], ["566", "567"], ["568", "569"], ["570", "571"], ["572", "573"], ["574", "575"], "'SUPPORTED_LANGUAGES' is defined but never used.", "'Language' is defined but never used.", ["576", "577"], ["578", "579"], ["580", "581"], ["582", "583"], ["584", "585"], ["586", "587"], ["588", "589"], ["590", "591"], ["592", "593"], ["594", "595"], {"messageId": "596", "fix": "597", "desc": "598"}, {"messageId": "599", "fix": "600", "desc": "601"}, {"messageId": "596", "fix": "602", "desc": "598"}, {"messageId": "599", "fix": "603", "desc": "601"}, {"messageId": "596", "fix": "604", "desc": "598"}, {"messageId": "599", "fix": "605", "desc": "601"}, {"messageId": "596", "fix": "606", "desc": "598"}, {"messageId": "599", "fix": "607", "desc": "601"}, {"messageId": "596", "fix": "608", "desc": "598"}, {"messageId": "599", "fix": "609", "desc": "601"}, {"messageId": "596", "fix": "610", "desc": "598"}, {"messageId": "599", "fix": "611", "desc": "601"}, {"messageId": "596", "fix": "612", "desc": "598"}, {"messageId": "599", "fix": "613", "desc": "601"}, {"desc": "614", "fix": "615"}, {"messageId": "596", "fix": "616", "desc": "598"}, {"messageId": "599", "fix": "617", "desc": "601"}, {"messageId": "596", "fix": "618", "desc": "598"}, {"messageId": "599", "fix": "619", "desc": "601"}, {"messageId": "596", "fix": "620", "desc": "598"}, {"messageId": "599", "fix": "621", "desc": "601"}, {"messageId": "596", "fix": "622", "desc": "598"}, {"messageId": "599", "fix": "623", "desc": "601"}, {"messageId": "596", "fix": "624", "desc": "598"}, {"messageId": "599", "fix": "625", "desc": "601"}, {"messageId": "596", "fix": "626", "desc": "598"}, {"messageId": "599", "fix": "627", "desc": "601"}, {"messageId": "596", "fix": "628", "desc": "598"}, {"messageId": "599", "fix": "629", "desc": "601"}, {"messageId": "596", "fix": "630", "desc": "598"}, {"messageId": "599", "fix": "631", "desc": "601"}, {"messageId": "596", "fix": "632", "desc": "598"}, {"messageId": "599", "fix": "633", "desc": "601"}, {"messageId": "596", "fix": "634", "desc": "598"}, {"messageId": "599", "fix": "635", "desc": "601"}, {"messageId": "596", "fix": "636", "desc": "598"}, {"messageId": "599", "fix": "637", "desc": "601"}, {"messageId": "596", "fix": "638", "desc": "598"}, {"messageId": "599", "fix": "639", "desc": "601"}, {"messageId": "596", "fix": "640", "desc": "598"}, {"messageId": "599", "fix": "641", "desc": "601"}, {"messageId": "596", "fix": "642", "desc": "598"}, {"messageId": "599", "fix": "643", "desc": "601"}, {"messageId": "596", "fix": "644", "desc": "598"}, {"messageId": "599", "fix": "645", "desc": "601"}, {"messageId": "596", "fix": "646", "desc": "598"}, {"messageId": "599", "fix": "647", "desc": "601"}, {"messageId": "596", "fix": "648", "desc": "598"}, {"messageId": "599", "fix": "649", "desc": "601"}, {"messageId": "596", "fix": "650", "desc": "598"}, {"messageId": "599", "fix": "651", "desc": "601"}, {"messageId": "596", "fix": "652", "desc": "598"}, {"messageId": "599", "fix": "653", "desc": "601"}, {"messageId": "596", "fix": "654", "desc": "598"}, {"messageId": "599", "fix": "655", "desc": "601"}, {"messageId": "596", "fix": "656", "desc": "598"}, {"messageId": "599", "fix": "657", "desc": "601"}, {"messageId": "596", "fix": "658", "desc": "598"}, {"messageId": "599", "fix": "659", "desc": "601"}, {"messageId": "596", "fix": "660", "desc": "598"}, {"messageId": "599", "fix": "661", "desc": "601"}, {"messageId": "596", "fix": "662", "desc": "598"}, {"messageId": "599", "fix": "663", "desc": "601"}, {"messageId": "596", "fix": "664", "desc": "598"}, {"messageId": "599", "fix": "665", "desc": "601"}, {"messageId": "596", "fix": "666", "desc": "598"}, {"messageId": "599", "fix": "667", "desc": "601"}, {"messageId": "596", "fix": "668", "desc": "598"}, {"messageId": "599", "fix": "669", "desc": "601"}, {"messageId": "596", "fix": "670", "desc": "598"}, {"messageId": "599", "fix": "671", "desc": "601"}, {"messageId": "596", "fix": "672", "desc": "598"}, {"messageId": "599", "fix": "673", "desc": "601"}, {"messageId": "596", "fix": "674", "desc": "598"}, {"messageId": "599", "fix": "675", "desc": "601"}, {"messageId": "596", "fix": "676", "desc": "598"}, {"messageId": "599", "fix": "677", "desc": "601"}, {"messageId": "596", "fix": "678", "desc": "598"}, {"messageId": "599", "fix": "679", "desc": "601"}, {"messageId": "596", "fix": "680", "desc": "598"}, {"messageId": "599", "fix": "681", "desc": "601"}, {"messageId": "596", "fix": "682", "desc": "598"}, {"messageId": "599", "fix": "683", "desc": "601"}, {"messageId": "596", "fix": "684", "desc": "598"}, {"messageId": "599", "fix": "685", "desc": "601"}, {"messageId": "596", "fix": "686", "desc": "598"}, {"messageId": "599", "fix": "687", "desc": "601"}, {"messageId": "596", "fix": "688", "desc": "598"}, {"messageId": "599", "fix": "689", "desc": "601"}, {"messageId": "596", "fix": "690", "desc": "598"}, {"messageId": "599", "fix": "691", "desc": "601"}, {"messageId": "596", "fix": "692", "desc": "598"}, {"messageId": "599", "fix": "693", "desc": "601"}, {"messageId": "596", "fix": "694", "desc": "598"}, {"messageId": "599", "fix": "695", "desc": "601"}, {"messageId": "596", "fix": "696", "desc": "598"}, {"messageId": "599", "fix": "697", "desc": "601"}, {"messageId": "596", "fix": "698", "desc": "598"}, {"messageId": "599", "fix": "699", "desc": "601"}, {"messageId": "596", "fix": "700", "desc": "598"}, {"messageId": "599", "fix": "701", "desc": "601"}, {"messageId": "596", "fix": "702", "desc": "598"}, {"messageId": "599", "fix": "703", "desc": "601"}, {"messageId": "596", "fix": "704", "desc": "598"}, {"messageId": "599", "fix": "705", "desc": "601"}, {"messageId": "596", "fix": "706", "desc": "598"}, {"messageId": "599", "fix": "707", "desc": "601"}, {"messageId": "596", "fix": "708", "desc": "598"}, {"messageId": "599", "fix": "709", "desc": "601"}, {"messageId": "596", "fix": "710", "desc": "598"}, {"messageId": "599", "fix": "711", "desc": "601"}, {"messageId": "596", "fix": "712", "desc": "598"}, {"messageId": "599", "fix": "713", "desc": "601"}, {"messageId": "596", "fix": "714", "desc": "598"}, {"messageId": "599", "fix": "715", "desc": "601"}, {"messageId": "596", "fix": "716", "desc": "598"}, {"messageId": "599", "fix": "717", "desc": "601"}, {"messageId": "596", "fix": "718", "desc": "598"}, {"messageId": "599", "fix": "719", "desc": "601"}, {"messageId": "596", "fix": "720", "desc": "598"}, {"messageId": "599", "fix": "721", "desc": "601"}, {"messageId": "596", "fix": "722", "desc": "598"}, {"messageId": "599", "fix": "723", "desc": "601"}, {"messageId": "596", "fix": "724", "desc": "598"}, {"messageId": "599", "fix": "725", "desc": "601"}, {"messageId": "596", "fix": "726", "desc": "598"}, {"messageId": "599", "fix": "727", "desc": "601"}, {"messageId": "596", "fix": "728", "desc": "598"}, {"messageId": "599", "fix": "729", "desc": "601"}, {"messageId": "596", "fix": "730", "desc": "598"}, {"messageId": "599", "fix": "731", "desc": "601"}, {"messageId": "596", "fix": "732", "desc": "598"}, {"messageId": "599", "fix": "733", "desc": "601"}, {"messageId": "596", "fix": "734", "desc": "598"}, {"messageId": "599", "fix": "735", "desc": "601"}, {"messageId": "596", "fix": "736", "desc": "598"}, {"messageId": "599", "fix": "737", "desc": "601"}, {"messageId": "596", "fix": "738", "desc": "598"}, {"messageId": "599", "fix": "739", "desc": "601"}, {"messageId": "596", "fix": "740", "desc": "598"}, {"messageId": "599", "fix": "741", "desc": "601"}, {"messageId": "596", "fix": "742", "desc": "598"}, {"messageId": "599", "fix": "743", "desc": "601"}, {"messageId": "596", "fix": "744", "desc": "598"}, {"messageId": "599", "fix": "745", "desc": "601"}, {"messageId": "596", "fix": "746", "desc": "598"}, {"messageId": "599", "fix": "747", "desc": "601"}, {"messageId": "596", "fix": "748", "desc": "598"}, {"messageId": "599", "fix": "749", "desc": "601"}, {"messageId": "596", "fix": "750", "desc": "598"}, {"messageId": "599", "fix": "751", "desc": "601"}, {"messageId": "596", "fix": "752", "desc": "598"}, {"messageId": "599", "fix": "753", "desc": "601"}, {"messageId": "596", "fix": "754", "desc": "598"}, {"messageId": "599", "fix": "755", "desc": "601"}, {"messageId": "596", "fix": "756", "desc": "598"}, {"messageId": "599", "fix": "757", "desc": "601"}, {"messageId": "596", "fix": "758", "desc": "598"}, {"messageId": "599", "fix": "759", "desc": "601"}, {"messageId": "596", "fix": "760", "desc": "598"}, {"messageId": "599", "fix": "761", "desc": "601"}, {"messageId": "596", "fix": "762", "desc": "598"}, {"messageId": "599", "fix": "763", "desc": "601"}, {"messageId": "596", "fix": "764", "desc": "598"}, {"messageId": "599", "fix": "765", "desc": "601"}, {"messageId": "596", "fix": "766", "desc": "598"}, {"messageId": "599", "fix": "767", "desc": "601"}, {"messageId": "596", "fix": "768", "desc": "598"}, {"messageId": "599", "fix": "769", "desc": "601"}, {"messageId": "596", "fix": "770", "desc": "598"}, {"messageId": "599", "fix": "771", "desc": "601"}, {"messageId": "596", "fix": "772", "desc": "598"}, {"messageId": "599", "fix": "773", "desc": "601"}, {"messageId": "596", "fix": "774", "desc": "598"}, {"messageId": "599", "fix": "775", "desc": "601"}, {"messageId": "596", "fix": "776", "desc": "598"}, {"messageId": "599", "fix": "777", "desc": "601"}, {"messageId": "596", "fix": "778", "desc": "598"}, {"messageId": "599", "fix": "779", "desc": "601"}, {"messageId": "596", "fix": "780", "desc": "598"}, {"messageId": "599", "fix": "781", "desc": "601"}, {"messageId": "596", "fix": "782", "desc": "598"}, {"messageId": "599", "fix": "783", "desc": "601"}, {"messageId": "596", "fix": "784", "desc": "598"}, {"messageId": "599", "fix": "785", "desc": "601"}, {"messageId": "596", "fix": "786", "desc": "598"}, {"messageId": "599", "fix": "787", "desc": "601"}, {"messageId": "596", "fix": "788", "desc": "598"}, {"messageId": "599", "fix": "789", "desc": "601"}, {"messageId": "596", "fix": "790", "desc": "598"}, {"messageId": "599", "fix": "791", "desc": "601"}, {"messageId": "596", "fix": "792", "desc": "598"}, {"messageId": "599", "fix": "793", "desc": "601"}, {"messageId": "596", "fix": "794", "desc": "598"}, {"messageId": "599", "fix": "795", "desc": "601"}, {"messageId": "596", "fix": "796", "desc": "598"}, {"messageId": "599", "fix": "797", "desc": "601"}, {"messageId": "596", "fix": "798", "desc": "598"}, {"messageId": "599", "fix": "799", "desc": "601"}, {"messageId": "596", "fix": "800", "desc": "598"}, {"messageId": "599", "fix": "801", "desc": "601"}, {"messageId": "596", "fix": "802", "desc": "598"}, {"messageId": "599", "fix": "803", "desc": "601"}, {"messageId": "596", "fix": "804", "desc": "598"}, {"messageId": "599", "fix": "805", "desc": "601"}, {"messageId": "596", "fix": "806", "desc": "598"}, {"messageId": "599", "fix": "807", "desc": "601"}, {"messageId": "596", "fix": "808", "desc": "598"}, {"messageId": "599", "fix": "809", "desc": "601"}, {"messageId": "596", "fix": "810", "desc": "598"}, {"messageId": "599", "fix": "811", "desc": "601"}, {"messageId": "596", "fix": "812", "desc": "598"}, {"messageId": "599", "fix": "813", "desc": "601"}, {"messageId": "596", "fix": "814", "desc": "598"}, {"messageId": "599", "fix": "815", "desc": "601"}, {"messageId": "596", "fix": "816", "desc": "598"}, {"messageId": "599", "fix": "817", "desc": "601"}, {"messageId": "596", "fix": "818", "desc": "598"}, {"messageId": "599", "fix": "819", "desc": "601"}, {"messageId": "596", "fix": "820", "desc": "598"}, {"messageId": "599", "fix": "821", "desc": "601"}, "suggestUnknown", {"range": "822", "text": "823"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "824", "text": "825"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "826", "text": "823"}, {"range": "827", "text": "825"}, {"range": "828", "text": "823"}, {"range": "829", "text": "825"}, {"range": "830", "text": "823"}, {"range": "831", "text": "825"}, {"range": "832", "text": "823"}, {"range": "833", "text": "825"}, {"range": "834", "text": "823"}, {"range": "835", "text": "825"}, {"range": "836", "text": "823"}, {"range": "837", "text": "825"}, "Update the dependencies array to be: [handleAuthStateChange, supabase.auth]", {"range": "838", "text": "839"}, {"range": "840", "text": "823"}, {"range": "841", "text": "825"}, {"range": "842", "text": "823"}, {"range": "843", "text": "825"}, {"range": "844", "text": "823"}, {"range": "845", "text": "825"}, {"range": "846", "text": "823"}, {"range": "847", "text": "825"}, {"range": "848", "text": "823"}, {"range": "849", "text": "825"}, {"range": "850", "text": "823"}, {"range": "851", "text": "825"}, {"range": "852", "text": "823"}, {"range": "853", "text": "825"}, {"range": "854", "text": "823"}, {"range": "855", "text": "825"}, {"range": "856", "text": "823"}, {"range": "857", "text": "825"}, {"range": "858", "text": "823"}, {"range": "859", "text": "825"}, {"range": "860", "text": "823"}, {"range": "861", "text": "825"}, {"range": "862", "text": "823"}, {"range": "863", "text": "825"}, {"range": "864", "text": "823"}, {"range": "865", "text": "825"}, {"range": "866", "text": "823"}, {"range": "867", "text": "825"}, {"range": "868", "text": "823"}, {"range": "869", "text": "825"}, {"range": "870", "text": "823"}, {"range": "871", "text": "825"}, {"range": "872", "text": "823"}, {"range": "873", "text": "825"}, {"range": "874", "text": "823"}, {"range": "875", "text": "825"}, {"range": "876", "text": "823"}, {"range": "877", "text": "825"}, {"range": "878", "text": "823"}, {"range": "879", "text": "825"}, {"range": "880", "text": "823"}, {"range": "881", "text": "825"}, {"range": "882", "text": "823"}, {"range": "883", "text": "825"}, {"range": "884", "text": "823"}, {"range": "885", "text": "825"}, {"range": "886", "text": "823"}, {"range": "887", "text": "825"}, {"range": "888", "text": "823"}, {"range": "889", "text": "825"}, {"range": "890", "text": "823"}, {"range": "891", "text": "825"}, {"range": "892", "text": "823"}, {"range": "893", "text": "825"}, {"range": "894", "text": "823"}, {"range": "895", "text": "825"}, {"range": "896", "text": "823"}, {"range": "897", "text": "825"}, {"range": "898", "text": "823"}, {"range": "899", "text": "825"}, {"range": "900", "text": "823"}, {"range": "901", "text": "825"}, {"range": "902", "text": "823"}, {"range": "903", "text": "825"}, {"range": "904", "text": "823"}, {"range": "905", "text": "825"}, {"range": "906", "text": "823"}, {"range": "907", "text": "825"}, {"range": "908", "text": "823"}, {"range": "909", "text": "825"}, {"range": "910", "text": "823"}, {"range": "911", "text": "825"}, {"range": "912", "text": "823"}, {"range": "913", "text": "825"}, {"range": "914", "text": "823"}, {"range": "915", "text": "825"}, {"range": "916", "text": "823"}, {"range": "917", "text": "825"}, {"range": "918", "text": "823"}, {"range": "919", "text": "825"}, {"range": "920", "text": "823"}, {"range": "921", "text": "825"}, {"range": "922", "text": "823"}, {"range": "923", "text": "825"}, {"range": "924", "text": "823"}, {"range": "925", "text": "825"}, {"range": "926", "text": "823"}, {"range": "927", "text": "825"}, {"range": "928", "text": "823"}, {"range": "929", "text": "825"}, {"range": "930", "text": "823"}, {"range": "931", "text": "825"}, {"range": "932", "text": "823"}, {"range": "933", "text": "825"}, {"range": "934", "text": "823"}, {"range": "935", "text": "825"}, {"range": "936", "text": "823"}, {"range": "937", "text": "825"}, {"range": "938", "text": "823"}, {"range": "939", "text": "825"}, {"range": "940", "text": "823"}, {"range": "941", "text": "825"}, {"range": "942", "text": "823"}, {"range": "943", "text": "825"}, {"range": "944", "text": "823"}, {"range": "945", "text": "825"}, {"range": "946", "text": "823"}, {"range": "947", "text": "825"}, {"range": "948", "text": "823"}, {"range": "949", "text": "825"}, {"range": "950", "text": "823"}, {"range": "951", "text": "825"}, {"range": "952", "text": "823"}, {"range": "953", "text": "825"}, {"range": "954", "text": "823"}, {"range": "955", "text": "825"}, {"range": "956", "text": "823"}, {"range": "957", "text": "825"}, {"range": "958", "text": "823"}, {"range": "959", "text": "825"}, {"range": "960", "text": "823"}, {"range": "961", "text": "825"}, {"range": "962", "text": "823"}, {"range": "963", "text": "825"}, {"range": "964", "text": "823"}, {"range": "965", "text": "825"}, {"range": "966", "text": "823"}, {"range": "967", "text": "825"}, {"range": "968", "text": "823"}, {"range": "969", "text": "825"}, {"range": "970", "text": "823"}, {"range": "971", "text": "825"}, {"range": "972", "text": "823"}, {"range": "973", "text": "825"}, {"range": "974", "text": "823"}, {"range": "975", "text": "825"}, {"range": "976", "text": "823"}, {"range": "977", "text": "825"}, {"range": "978", "text": "823"}, {"range": "979", "text": "825"}, {"range": "980", "text": "823"}, {"range": "981", "text": "825"}, {"range": "982", "text": "823"}, {"range": "983", "text": "825"}, {"range": "984", "text": "823"}, {"range": "985", "text": "825"}, {"range": "986", "text": "823"}, {"range": "987", "text": "825"}, {"range": "988", "text": "823"}, {"range": "989", "text": "825"}, {"range": "990", "text": "823"}, {"range": "991", "text": "825"}, {"range": "992", "text": "823"}, {"range": "993", "text": "825"}, {"range": "994", "text": "823"}, {"range": "995", "text": "825"}, {"range": "996", "text": "823"}, {"range": "997", "text": "825"}, {"range": "998", "text": "823"}, {"range": "999", "text": "825"}, {"range": "1000", "text": "823"}, {"range": "1001", "text": "825"}, {"range": "1002", "text": "823"}, {"range": "1003", "text": "825"}, {"range": "1004", "text": "823"}, {"range": "1005", "text": "825"}, {"range": "1006", "text": "823"}, {"range": "1007", "text": "825"}, {"range": "1008", "text": "823"}, {"range": "1009", "text": "825"}, {"range": "1010", "text": "823"}, {"range": "1011", "text": "825"}, {"range": "1012", "text": "823"}, {"range": "1013", "text": "825"}, {"range": "1014", "text": "823"}, {"range": "1015", "text": "825"}, {"range": "1016", "text": "823"}, {"range": "1017", "text": "825"}, {"range": "1018", "text": "823"}, {"range": "1019", "text": "825"}, {"range": "1020", "text": "823"}, {"range": "1021", "text": "825"}, {"range": "1022", "text": "823"}, {"range": "1023", "text": "825"}, {"range": "1024", "text": "823"}, {"range": "1025", "text": "825"}, {"range": "1026", "text": "823"}, {"range": "1027", "text": "825"}, {"range": "1028", "text": "823"}, {"range": "1029", "text": "825"}, {"range": "1030", "text": "823"}, {"range": "1031", "text": "825"}, {"range": "1032", "text": "823"}, {"range": "1033", "text": "825"}, {"range": "1034", "text": "823"}, {"range": "1035", "text": "825"}, {"range": "1036", "text": "823"}, {"range": "1037", "text": "825"}, {"range": "1038", "text": "823"}, {"range": "1039", "text": "825"}, {"range": "1040", "text": "823"}, {"range": "1041", "text": "825"}, {"range": "1042", "text": "823"}, {"range": "1043", "text": "825"}, {"range": "1044", "text": "823"}, {"range": "1045", "text": "825"}, [2609, 2612], "unknown", [2609, 2612], "never", [2620, 2623], [2620, 2623], [438, 441], [438, 441], [477, 480], [477, 480], [537, 540], [537, 540], [553, 556], [553, 556], [603, 606], [603, 606], [2040, 2055], "[handleAuthStateChange, supabase.auth]", [5672, 5675], [5672, 5675], [618, 621], [618, 621], [626, 629], [626, 629], [3344, 3347], [3344, 3347], [3372, 3375], [3372, 3375], [3620, 3623], [3620, 3623], [3648, 3651], [3648, 3651], [3838, 3841], [3838, 3841], [3866, 3869], [3866, 3869], [4844, 4847], [4844, 4847], [6042, 6045], [6042, 6045], [6248, 6251], [6248, 6251], [6610, 6613], [6610, 6613], [6747, 6750], [6747, 6750], [6879, 6882], [6879, 6882], [7139, 7142], [7139, 7142], [7367, 7370], [7367, 7370], [7610, 7613], [7610, 7613], [7799, 7802], [7799, 7802], [7807, 7810], [7807, 7810], [398, 401], [398, 401], [410, 413], [410, 413], [859, 862], [859, 862], [871, 874], [871, 874], [1301, 1304], [1301, 1304], [1313, 1316], [1313, 1316], [1813, 1816], [1813, 1816], [1825, 1828], [1825, 1828], [2296, 2299], [2296, 2299], [2308, 2311], [2308, 2311], [4730, 4733], [4730, 4733], [5784, 5787], [5784, 5787], [6014, 6017], [6014, 6017], [6033, 6036], [6033, 6036], [6058, 6061], [6058, 6061], [6081, 6084], [6081, 6084], [6103, 6106], [6103, 6106], [11846, 11849], [11846, 11849], [467, 470], [467, 470], [2044, 2047], [2044, 2047], [2077, 2080], [2077, 2080], [2775, 2778], [2775, 2778], [2830, 2833], [2830, 2833], [4755, 4758], [4755, 4758], [4802, 4805], [4802, 4805], [5220, 5223], [5220, 5223], [5277, 5280], [5277, 5280], [6088, 6091], [6088, 6091], [6139, 6142], [6139, 6142], [6574, 6577], [6574, 6577], [6624, 6627], [6624, 6627], [6661, 6664], [6661, 6664], [7382, 7385], [7382, 7385], [7807, 7810], [7807, 7810], [3017, 3020], [3017, 3020], [3532, 3535], [3532, 3535], [4557, 4560], [4557, 4560], [4711, 4714], [4711, 4714], [5673, 5676], [5673, 5676], [5679, 5682], [5679, 5682], [6619, 6622], [6619, 6622], [6625, 6628], [6625, 6628], [7997, 8000], [7997, 8000], [8003, 8006], [8003, 8006], [8527, 8530], [8527, 8530], [8533, 8536], [8533, 8536], [574, 577], [574, 577], [641, 644], [641, 644], [1234, 1237], [1234, 1237], [1508, 1511], [1508, 1511], [1767, 1770], [1767, 1770], [2044, 2047], [2044, 2047], [481, 484], [481, 484], [801, 804], [801, 804], [1128, 1131], [1128, 1131], [3667, 3670], [3667, 3670], [3698, 3701], [3698, 3701], [3815, 3818], [3815, 3818], [4409, 4412], [4409, 4412], [4441, 4444], [4441, 4444], [4561, 4564], [4561, 4564], [5162, 5165], [5162, 5165], [5194, 5197], [5194, 5197], [5314, 5317], [5314, 5317], [8925, 8928], [8925, 8928], [9322, 9325], [9322, 9325], [9952, 9955], [9952, 9955], [10960, 10963], [10960, 10963], [11447, 11450], [11447, 11450], [11779, 11782], [11779, 11782], [11808, 11811], [11808, 11811], [12296, 12299], [12296, 12299], [12682, 12685], [12682, 12685], [6711, 6714], [6711, 6714], [7478, 7481], [7478, 7481], [10201, 10204], [10201, 10204], [10211, 10214], [10211, 10214], [10510, 10513], [10510, 10513], [10520, 10523], [10520, 10523], [1938, 1941], [1938, 1941], [8251, 8254], [8251, 8254], [8614, 8617], [8614, 8617], [9105, 9108], [9105, 9108]]