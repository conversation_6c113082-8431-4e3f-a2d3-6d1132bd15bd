/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CFAWZY%5CNew%20folder%20(3)%5Cdentopro-saas%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CFAWZY%5CNew%20folder%20(3)%5Cdentopro-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CFAWZY%5CNew%20folder%20(3)%5Cdentopro-saas%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CFAWZY%5CNew%20folder%20(3)%5Cdentopro-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(rsc)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/experimental/ppr */ \"(rsc)/./node_modules/next/dist/server/lib/experimental/ppr.js\");\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/request/fallback-params */ \"(rsc)/./node_modules/next/dist/server/request/fallback-params.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/app-render/encryption-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption-utils.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/lib/streaming-metadata */ \"(rsc)/./node_modules/next/dist/server/lib/streaming-metadata.js\");\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/app-render/action-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/action-utils.js\");\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/server-action-request-meta */ \"(rsc)/./node_modules/next/dist/server/lib/server-action-request-meta.js\");\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/client/components/app-router-headers */ \"(rsc)/./node_modules/next/dist/client/components/app-router-headers.js\");\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/lib/fallback */ \"(rsc)/./node_modules/next/dist/lib/fallback.js\");\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(rsc)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/server/stream-utils/encoded-tags */ \"(rsc)/./node_modules/next/dist/server/stream-utils/encoded-tags.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(rsc)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\");\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(rsc)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\",\"handler\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"next/dist/client/components/builtin/global-error.js\"],\n'not-found': [module2, \"next/dist/client/components/builtin/not-found.js\"],\n'forbidden': [module3, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module4, \"next/dist/client/components/builtin/unauthorized.js\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\"];\n\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    var _this;\n    let srcPage = \"/page\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const initialPostponed = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'postponed');\n    // TODO: replace with more specific flags\n    const minimalMode = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'minimalMode');\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, query, params, parsedUrl, pageIsDynamic, buildManifest, nextFontManifest, reactLoadableManifest, serverActionsManifest, clientReferenceManifest, subresourceIntegrityManifest, prerenderManifest, isDraftMode, resolvedPathname, revalidateOnlyGenerated, routerServerContext, nextConfig } = prepareResult;\n    const pathname = parsedUrl.pathname || '/';\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__.normalizeAppPath)(srcPage);\n    let { isOnDemandRevalidate } = prepareResult;\n    const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n    const isPrerendered = prerenderManifest.routes[resolvedPathname];\n    let isSSG = Boolean(prerenderInfo || isPrerendered || prerenderManifest.routes[normalizedSrcPage]);\n    const userAgent = req.headers['user-agent'] || '';\n    const botType = (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.getBotType)(userAgent);\n    const isHtmlBot = (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.isHtmlBotRequest)(req);\n    /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */ const isPrefetchRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isPrefetchRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_ROUTER_PREFETCH_HEADER]);\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.RSC_HEADER]);\n    const isPossibleServerAction = (0,next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__.getIsPossibleServerAction)(req);\n    /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */ const couldSupportPPR = (0,next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__.checkIsAppPPREnabled)(nextConfig.experimental.ppr);\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =  false && 0;\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[normalizedSrcPage] ?? prerenderManifest.dynamicRoutes[normalizedSrcPage]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR\n    // enabled or not, but that would require plumbing the appConfig through\n    // to the server during development. We assume that the page supports it\n    // but only during development.\n    hasDebugStaticShellQuery && (routeModule.isDev === true || (routerServerContext == null ? void 0 : routerServerContext.experimentalTestProxy) === true));\n    const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses = isDebugStaticShell && routeModule.isDev === true;\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined;\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'segmentPrefetchRSCRequest');\n    // TODO: investigate existing bug with shouldServeStreamingMetadata always\n    // being true for a revalidate due to modifying the base-server this.renderOpts\n    // when fixing this to correct logic it causes hydration issue since we set\n    // serveStreamingMetadata to true during export\n    let serveStreamingMetadata = !userAgent ? true : (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.shouldServeStreamingMetadata)(userAgent, nextConfig.htmlLimitedBots);\n    if (isHtmlBot && isRoutePPREnabled) {\n        isSSG = false;\n        serveStreamingMetadata = false;\n    }\n    // In development, we always want to generate dynamic HTML.\n    let supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG || // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' || // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest;\n    // When html bots request PPR page, perform the full dynamic rendering.\n    const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;\n    let ssgCacheKey = null;\n    if (!isDraftMode && isSSG && !supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {\n        ssgCacheKey = resolvedPathname;\n    }\n    // the staticPathKey differs from ssgCacheKey since\n    // ssgCacheKey is null in dev since we're always in \"dynamic\"\n    // mode in dev to bypass the cache, but we still need to honor\n    // dynamicParams = false in dev mode\n    let staticPathKey = ssgCacheKey;\n    if (!staticPathKey && routeModule.isDev) {\n        staticPathKey = resolvedPathname;\n    }\n    const ComponentMod = {\n        ...next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__,\n        tree,\n        pages,\n        GlobalError: (next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default()),\n        handler,\n        routeModule,\n        __next_app__\n    };\n    // Before rendering (which initializes component tree modules), we have to\n    // set the reference manifests to our global store so Server Action's\n    // encryption util can access to them at the top level of the page module.\n    if (serverActionsManifest && clientReferenceManifest) {\n        (0,next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__.setReferenceManifestsSingleton)({\n            page: srcPage,\n            clientReferenceManifest,\n            serverActionsManifest,\n            serverModuleMap: (0,next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__.createServerModuleMap)({\n                serverActionsManifest\n            })\n        });\n    }\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const invokeRouteModule = async (span, context)=>{\n            const nextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextRequest(req);\n            const nextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextResponse(res);\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (true) {\n                if (nextConfig.experimental.dynamicIO && !isPrefetchRSCRequest && !context.renderOpts.isPossibleServerAction) {\n                    const warmup = await routeModule.warmup(nextReq, nextRes, context);\n                    // If the warmup is successful, we should use the resume data\n                    // cache from the warmup.\n                    if (warmup.metadata.renderResumeDataCache) {\n                        context.renderOpts.renderResumeDataCache = warmup.metadata.renderResumeDataCache;\n                    }\n                }\n            }\n            return routeModule.render(nextReq, nextRes, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const doRender = async ({ span, postponed, fallbackRouteParams })=>{\n            const context = {\n                query,\n                params,\n                page: normalizedSrcPage,\n                sharedContext: {\n                    buildId\n                },\n                serverComponentsHmrCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'serverComponentsHmrCache'),\n                fallbackRouteParams,\n                renderOpts: {\n                    App: ()=>null,\n                    Document: ()=>null,\n                    pageConfig: {},\n                    ComponentMod,\n                    Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(ComponentMod),\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    postponed,\n                    shouldWaitOnAllReady,\n                    serveStreamingMetadata,\n                    supportsDynamicResponse: typeof postponed === 'string' || supportsDynamicResponse,\n                    buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n                    subresourceIntegrityManifest,\n                    serverActionsManifest,\n                    clientReferenceManifest,\n                    setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                    dir: routeModule.projectDir,\n                    isDraftMode,\n                    isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n                    botType,\n                    isOnDemandRevalidate,\n                    isPossibleServerAction,\n                    assetPrefix: nextConfig.assetPrefix,\n                    nextConfigOutput: nextConfig.output,\n                    crossOrigin: nextConfig.crossOrigin,\n                    trailingSlash: nextConfig.trailingSlash,\n                    previewProps: prerenderManifest.preview,\n                    deploymentId: nextConfig.deploymentId,\n                    enableTainting: nextConfig.experimental.taint,\n                    htmlLimitedBots: nextConfig.htmlLimitedBots,\n                    devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n                    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n                    multiZoneDraftMode,\n                    incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'incrementalCache'),\n                    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n                    basePath: nextConfig.basePath,\n                    serverActions: nextConfig.experimental.serverActions,\n                    ...isDebugStaticShell || isDebugDynamicAccesses ? {\n                        nextExport: true,\n                        supportsDynamicResponse: false,\n                        isStaticGeneration: true,\n                        isRevalidate: true,\n                        isDebugDynamicAccesses: isDebugDynamicAccesses\n                    } : {},\n                    experimental: {\n                        isRoutePPREnabled,\n                        expireTime: nextConfig.expireTime,\n                        staleTimes: nextConfig.experimental.staleTimes,\n                        dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n                        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n                        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n                        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n                        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                    },\n                    waitUntil: ctx.waitUntil,\n                    onClose: (cb)=>{\n                        res.on('close', cb);\n                    },\n                    onAfterTaskError: ()=>{},\n                    onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext),\n                    err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'invokeError'),\n                    dev: routeModule.isDev\n                }\n            };\n            const result = await invokeRouteModule(span, context);\n            const { metadata } = result;\n            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.\n            fetchTags: cacheTags } = metadata;\n            if (cacheTags) {\n                headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n            }\n            // Pull any fetch metrics from the render onto the request.\n            ;\n            req.fetchMetrics = metadata.fetchMetrics;\n            // we don't throw static to dynamic errors in dev as isSSG\n            // is a best guess in dev since we don't have the prerender pass\n            // to know whether the path is actually static or not\n            if (isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !routeModule.isDev && !isRoutePPREnabled) {\n                const staticBailoutInfo = metadata.staticBailoutInfo;\n                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${resolvedPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E132\",\n                    enumerable: false,\n                    configurable: true\n                });\n                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {\n                    const stack = staticBailoutInfo.stack;\n                    err.stack = err.message + stack.substring(stack.indexOf('\\n'));\n                }\n                throw err;\n            }\n            return {\n                value: {\n                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE,\n                    html: result,\n                    headers,\n                    rscData: metadata.flightData,\n                    postponed: metadata.postponed,\n                    status: metadata.statusCode,\n                    segmentData: metadata.segmentData\n                },\n                cacheControl\n            };\n        };\n        const responseGenerator = async ({ hasResolved, previousCacheEntry, isRevalidating, span })=>{\n            const isProduction = routeModule.isDev === false;\n            const didRespond = hasResolved || res.writableEnded;\n            // skip on-demand revalidate if cache is not present and\n            // revalidate-if-generated is set\n            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry && !minimalMode) {\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res);\n                } else {\n                    res.statusCode = 404;\n                    res.end('This page could not be found');\n                }\n                return null;\n            }\n            let fallbackMode;\n            if (prerenderInfo) {\n                fallbackMode = (0,next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.parseFallbackField)(prerenderInfo.fallback);\n            }\n            // When serving a bot request, we want to serve a blocking render and not\n            // the prerendered page. This ensures that the correct content is served\n            // to the bot in the head.\n            if (fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.PRERENDER && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.isBot)(userAgent)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if ((previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) === -1) {\n                isOnDemandRevalidate = true;\n            }\n            // TODO: adapt for PPR\n            // only allow on-demand revalidate for fallback: true/blocking\n            // or for prerendered fallback: false paths\n            if (isOnDemandRevalidate && (fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND || previousCacheEntry)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if (!minimalMode && fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isDraftMode && pageIsDynamic && (isProduction || !isPrerendered)) {\n                // if the page has dynamicParams: false and this pathname wasn't\n                // prerendered trigger the no fallback handling\n                if (// In development, fall through to render to handle missing\n                // getStaticPaths.\n                (isProduction || prerenderInfo) && // When fallback isn't present, abort this render so we 404\n                fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND) {\n                    throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError();\n                }\n                let fallbackResponse;\n                if (isRoutePPREnabled && !isRSCRequest) {\n                    // We use the response cache here to handle the revalidation and\n                    // management of the fallback shell.\n                    fallbackResponse = await routeModule.handleResponse({\n                        cacheKey: isProduction ? normalizedSrcPage : null,\n                        req,\n                        nextConfig,\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                        isFallback: true,\n                        prerenderManifest,\n                        isRoutePPREnabled,\n                        responseGenerator: async ()=>doRender({\n                                span,\n                                // We pass `undefined` as rendering a fallback isn't resumed\n                                // here.\n                                postponed: undefined,\n                                fallbackRouteParams: // If we're in production or we're debugging the fallback\n                                // shell then we should postpone when dynamic params are\n                                // accessed.\n                                isProduction || isDebugFallbackShell ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(normalizedSrcPage) : null\n                            }),\n                        waitUntil: ctx.waitUntil\n                    });\n                    // If the fallback response was set to null, then we should return null.\n                    if (fallbackResponse === null) return null;\n                    // Otherwise, if we did get a fallback response, we should return it.\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        return fallbackResponse;\n                    }\n                }\n            }\n            // Only requests that aren't revalidating can be resumed. If we have the\n            // minimal postponed data, then we should resume the render with it.\n            const postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;\n            // When we're in minimal mode, if we're trying to debug the static shell,\n            // we should just return nothing instead of resuming the dynamic render.\n            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {\n                return {\n                    cacheControl: {\n                        revalidate: 1,\n                        expire: undefined\n                    },\n                    value: {\n                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.PAGES,\n                        html: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                        pageData: {},\n                        headers: undefined,\n                        status: undefined\n                    }\n                };\n            }\n            // If this is a dynamic route with PPR enabled and the default route\n            // matches were set, then we should pass the fallback route params to\n            // the renderer as this is a fallback revalidation request.\n            const fallbackRouteParams = pageIsDynamic && isRoutePPREnabled && ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'renderFallbackShell') || isDebugFallbackShell) ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(pathname) : null;\n            // Perform the render.\n            return doRender({\n                span,\n                postponed,\n                fallbackRouteParams\n            });\n        };\n        const handleResponse = async (span)=>{\n            var _cacheEntry_value, _cachedData_headers;\n            const cacheEntry = await routeModule.handleResponse({\n                cacheKey: ssgCacheKey,\n                responseGenerator: (c)=>responseGenerator({\n                        span,\n                        ...c\n                    }),\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                isOnDemandRevalidate,\n                isRoutePPREnabled,\n                req,\n                nextConfig,\n                prerenderManifest,\n                waitUntil: ctx.waitUntil\n            });\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            if (!cacheEntry) {\n                if (ssgCacheKey) {\n                    // A cache entry might not be generated if a response is written\n                    // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n                    // have a cache key. If we do have a cache key but we don't end up\n                    // with a cache entry, then either Next.js or the application has a\n                    // bug that needs fixing.\n                    throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E62\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                return null;\n            }\n            if (((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant app-page handler received invalid cache entry ${(_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E707\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            const didPostpone = typeof cacheEntry.value.postponed === 'string';\n            if (isSSG && // We don't want to send a cache header for requests that contain dynamic\n            // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n            // request, then we should set the cache header.\n            !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {\n                if (!minimalMode) {\n                    // set x-nextjs-cache header to match the header\n                    // we set for the image-optimizer\n                    res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n                }\n                // Set a header used by the client router to signal the response is static\n                // and should respect the `static` cache staleTime value.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_IS_PRERENDER_HEADER, '1');\n            }\n            const { value: cachedData } = cacheEntry;\n            // Coerce the cache control parameter from the render.\n            let cacheControl;\n            // If this is a resume request in minimal mode it is streamed with dynamic\n            // content and should not be cached.\n            if (minimalPostponed) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (minimalMode && isRSCRequest && !isPrefetchRSCRequest && isRoutePPREnabled) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (!routeModule.isDev) {\n                // If this is a preview mode request, we shouldn't cache it\n                if (isDraftMode) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                } else if (!isSSG) {\n                    if (!res.getHeader('Cache-Control')) {\n                        cacheControl = {\n                            revalidate: 0,\n                            expire: undefined\n                        };\n                    }\n                } else if (cacheEntry.cacheControl) {\n                    // If the cache entry has a cache control with a revalidate value that's\n                    // a number, use it.\n                    if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n                        var _cacheEntry_cacheControl;\n                        if (cacheEntry.cacheControl.revalidate < 1) {\n                            throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E22\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheControl = {\n                            revalidate: cacheEntry.cacheControl.revalidate,\n                            expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? nextConfig.expireTime\n                        };\n                    } else {\n                        cacheControl = {\n                            revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.CACHE_ONE_YEAR,\n                            expire: undefined\n                        };\n                    }\n                }\n            }\n            cacheEntry.cacheControl = cacheControl;\n            if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE && cachedData.segmentData) {\n                var _cachedData_headers1;\n                // This is a prefetch request issued by the client Segment Cache. These\n                // should never reach the application layer (lambda). We should either\n                // respond from the cache (HIT) or respond with 204 No Content (MISS).\n                // Set a header to indicate that PPR is enabled for this route. This\n                // lets the client distinguish between a regular cache miss and a cache\n                // miss due to PPR being disabled. In other contexts this header is used\n                // to indicate that the response contains dynamic data, but here we're\n                // only using it to indicate that the feature is enabled — the segment\n                // response itself contains whether the data is dynamic.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '2');\n                // Add the cache tags header to the response if it exists and we're in\n                // minimal mode while rendering a static page.\n                const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                    res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n                }\n                const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);\n                if (matchedSegment !== undefined) {\n                    // Cache hit\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(matchedSegment),\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // Cache miss. Either a cache entry for this route has not been generated\n                // (which technically should not be possible when PPR is enabled, because\n                // at a minimum there should always be a fallback entry) or there's no\n                // match for the requested segment. Respond with a 204 No Content. We\n                // don't bother to respond with 404, because these requests are only\n                // issued as part of a prefetch.\n                res.statusCode = 204;\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If there's a callback for `onCacheEntry`, call it with the cache entry\n            // and the revalidate options.\n            const onCacheEntry = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'onCacheEntry');\n            if (onCacheEntry) {\n                const finished = await onCacheEntry({\n                    ...cacheEntry,\n                    // TODO: remove this when upstream doesn't\n                    // always expect this value to be \"PAGE\"\n                    value: {\n                        ...cacheEntry.value,\n                        kind: 'PAGE'\n                    }\n                }, {\n                    url: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'initURL')\n                });\n                if (finished) {\n                    // TODO: maybe we have to end the request?\n                    return null;\n                }\n            }\n            // If the request has a postponed state and it's a resume request we\n            // should error.\n            if (didPostpone && minimalPostponed) {\n                throw Object.defineProperty(new Error('Invariant: postponed state should not be present on a resume request'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E396\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (cachedData.headers) {\n                const headers = {\n                    ...cachedData.headers\n                };\n                if (!minimalMode || !isSSG) {\n                    delete headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                }\n                for (let [key, value] of Object.entries(headers)){\n                    if (typeof value === 'undefined') continue;\n                    if (Array.isArray(value)) {\n                        for (const v of value){\n                            res.appendHeader(key, v);\n                        }\n                    } else if (typeof value === 'number') {\n                        value = value.toString();\n                        res.appendHeader(key, value);\n                    } else {\n                        res.appendHeader(key, value);\n                    }\n                }\n            }\n            // Add the cache tags header to the response if it exists and we're in\n            // minimal mode while rendering a static page.\n            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n            if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n            }\n            // If the request is a data request, then we shouldn't set the status code\n            // from the response because it should always be 200. This should be gated\n            // behind the experimental PPR flag.\n            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n                res.statusCode = cachedData.status;\n            }\n            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n            if (!minimalMode && cachedData.status && next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__.RedirectStatusCode[cachedData.status] && isRSCRequest) {\n                res.statusCode = 200;\n            }\n            // Mark that the request did postpone.\n            if (didPostpone) {\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '1');\n            }\n            // we don't go through this block when preview mode is true\n            // as preview mode is a dynamic request (bypasses cache) and doesn't\n            // generate both HTML and payloads in the same request so continue to just\n            // return the generated payload\n            if (isRSCRequest && !isDraftMode) {\n                // If this is a dynamic RSC request, then stream the response.\n                if (typeof cachedData.rscData === 'undefined') {\n                    if (cachedData.postponed) {\n                        throw Object.defineProperty(new Error('Invariant: Expected postponed to be undefined'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E372\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: cachedData.html,\n                        // Dynamic RSC responses cannot be cached, even if they're\n                        // configured with `force-static` because we have no way of\n                        // distinguishing between `force-static` and pages that have no\n                        // postponed state.\n                        // TODO: distinguish `force-static` from pages with no postponed state (static)\n                        cacheControl: isDynamicRSCRequest ? {\n                            revalidate: 0,\n                            expire: undefined\n                        } : cacheEntry.cacheControl\n                    });\n                }\n                // As this isn't a prefetch request, we should serve the static flight\n                // data.\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(cachedData.rscData),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // This is a request for HTML data.\n            let body = cachedData.html;\n            // If there's no postponed state, we should just serve the HTML. This\n            // should also be the case for a resume request because it's completed\n            // as a server render (rather than a static render).\n            if (!didPostpone || minimalMode) {\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If we're debugging the static shell or the dynamic API accesses, we\n            // should just serve the HTML without resuming the render. The returned\n            // HTML will be the static shell so all the Dynamic API's will be used\n            // during static generation.\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                // Since we're not resuming the render, we need to at least add the\n                // closing body and html tags to create valid HTML.\n                body.chain(new ReadableStream({\n                    start (controller) {\n                        controller.enqueue(next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__.ENCODED_TAGS.CLOSED.BODY_AND_HTML);\n                        controller.close();\n                    }\n                }));\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: {\n                        revalidate: 0,\n                        expire: undefined\n                    }\n                });\n            }\n            // This request has postponed, so let's create a new transformer that the\n            // dynamic data can pipe to that will attach the dynamic data to the end\n            // of the response.\n            const transformer = new TransformStream();\n            body.chain(transformer.readable);\n            // Perform the render again, but this time, provide the postponed state.\n            // We don't await because we want the result to start streaming now, and\n            // we've already chained the transformer's readable to the render result.\n            doRender({\n                span,\n                postponed: cachedData.postponed,\n                // This is a resume render, not a fallback render, so we don't need to\n                // set this.\n                fallbackRouteParams: null\n            }).then(async (result)=>{\n                var _result_value;\n                if (!result) {\n                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E463\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                    var _result_value1;\n                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E305\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // Pipe the resume result to the transformer.\n                await result.value.html.pipeTo(transformer.writable);\n            }).catch((err)=>{\n                // An error occurred during piping or preparing the render, abort\n                // the transformers writer so we can terminate the stream.\n                transformer.writable.abort(err).catch((e)=>{\n                    console.error(\"couldn't abort transformer\", e);\n                });\n            });\n            return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                req,\n                res,\n                type: 'html',\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                result: body,\n                // We don't want to cache the response if it has postponed data because\n                // the response being sent to the client it's dynamic parts are streamed\n                // to the client on the same request.\n                cacheControl: {\n                    revalidate: 0,\n                    expire: undefined\n                }\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            return await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__.getRevalidateReason)({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CFAWZY%5CNew%20folder%20(3)%5Cdentopro-saas%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CFAWZY%5CNew%20folder%20(3)%5Cdentopro-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNGQVdaWSU1QyU1Q05ldyUyMGZvbGRlciUyMCgzKSU1QyU1Q2RlbnRvcHJvLXNhYXMlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2FwcC1kaXIlNUMlNUNsaW5rLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyX19lc01vZHVsZSUyMiUyQyUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdOQUEySyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiX19lc01vZHVsZVwiLFwiZGVmYXVsdFwiXSAqLyBcIkQ6XFxcXEZBV1pZXFxcXE5ldyBmb2xkZXIgKDMpXFxcXGRlbnRvcHJvLXNhYXNcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcYXBwLWRpclxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Csrc%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Csrc%5C%5Chooks%5C%5CuseAuth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Csrc%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Csrc%5C%5Chooks%5C%5CuseAuth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/ThemeContext.tsx */ \"(rsc)/./src/contexts/ThemeContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/hooks/useAuth.tsx */ \"(rsc)/./src/hooks/useAuth.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNGQVdaWSU1QyU1Q05ldyUyMGZvbGRlciUyMCgzKSU1QyU1Q2RlbnRvcHJvLXNhYXMlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDRkFXWlklNUMlNUNOZXclMjBmb2xkZXIlMjAoMyklNUMlNUNkZW50b3Byby1zYWFzJTVDJTVDc3JjJTVDJTVDY29udGV4dHMlNUMlNUNUaGVtZUNvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVGhlbWVQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDRkFXWlklNUMlNUNOZXclMjBmb2xkZXIlMjAoMyklNUMlNUNkZW50b3Byby1zYWFzJTVDJTVDc3JjJTVDJTVDaG9va3MlNUMlNUN1c2VBdXRoLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEtBQWlKO0FBQ2pKO0FBQ0EsMEpBQXdJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUaGVtZVByb3ZpZGVyXCJdICovIFwiRDpcXFxcRkFXWllcXFxcTmV3IGZvbGRlciAoMylcXFxcZGVudG9wcm8tc2Fhc1xcXFxzcmNcXFxcY29udGV4dHNcXFxcVGhlbWVDb250ZXh0LnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiRDpcXFxcRkFXWllcXFxcTmV3IGZvbGRlciAoMylcXFxcZGVudG9wcm8tc2Fhc1xcXFxzcmNcXFxcaG9va3NcXFxcdXNlQXV0aC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Csrc%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Csrc%5C%5Chooks%5C%5CuseAuth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxGQVdaWVxcTmV3IGZvbGRlciAoMylcXGRlbnRvcHJvLXNhYXNcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"84685e205b07\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcRkFXWllcXE5ldyBmb2xkZXIgKDMpXFxkZW50b3Byby1zYWFzXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4NDY4NWUyMDViMDdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(rsc)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(rsc)/./src/contexts/ThemeContext.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/constants */ \"(rsc)/./src/lib/constants.ts\");\n\n\n\n\n\nconst metadata = {\n    title: `${_lib_constants__WEBPACK_IMPORTED_MODULE_4__.APP_NAME} - Complete Dental Clinic Management`,\n    description: \"Streamline your dental practice with our comprehensive SaaS platform. Manage patients, appointments, treatments, inventory, and more - all in one place.\",\n    keywords: \"dental clinic management, dental software, patient management, appointment scheduling, dental SaaS\",\n    authors: [\n        {\n            name: \"DentoPro Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    robots: \"index, follow\",\n    openGraph: {\n        title: `${_lib_constants__WEBPACK_IMPORTED_MODULE_4__.APP_NAME} - Complete Dental Clinic Management`,\n        description: \"Streamline your dental practice with our comprehensive SaaS platform.\",\n        type: \"website\",\n        locale: \"en_US\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: `${_lib_constants__WEBPACK_IMPORTED_MODULE_4__.APP_NAME} - Complete Dental Clinic Management`,\n        description: \"Streamline your dental practice with our comprehensive SaaS platform.\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"antialiased\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.ThemeProvider, {\n                defaultTheme: \"system\",\n                storageKey: \"dentopro-ui-theme\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(rsc)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/constants */ \"(rsc)/./src/lib/constants.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-blue-600\",\n                                        children: _lib_constants__WEBPACK_IMPORTED_MODULE_2__.APP_NAME\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 13,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-sm text-gray-500\",\n                                        children: \"SaaS Platform\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 14,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 12,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/auth/login\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            variant: \"outline\",\n                                            children: \"Doctor Login\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 18,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 17,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/patient/book-appointment\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            children: \"Book Appointment\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 21,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"Complete Dental Clinic Management\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\",\n                            children: \"Streamline your dental practice with our comprehensive SaaS platform. Manage patients, appointments, treatments, inventory, and more - all in one place.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-yellow-800 mb-2\",\n                                    children: \"\\uD83D\\uDEA7 Development in Progress\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Phase 1: Project Foundation & Architecture\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" - Day 1 Complete ✅\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 text-sm text-yellow-600\",\n                                    children: [\n                                        \"❌ Next.js 15 + TypeScript setup - NEEDS FIXING\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 61\n                                        }, this),\n                                        \"❌ Tailwind CSS configuration - BROKEN\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 52\n                                        }, this),\n                                        \"❌ Project structure & types - INCOMPLETE\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 55\n                                        }, this),\n                                        \"❌ Database setup - NOT VERIFIED\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 46\n                                        }, this),\n                                        \"\\uD83D\\uDEA8 Currently fixing: Critical build issues\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white p-6 rounded-lg shadow-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4 mx-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-blue-600\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 59,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"Patient Management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Complete patient records, medical history, and file management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white p-6 rounded-lg shadow-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4 mx-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-green-600\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"Appointment Scheduling\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Smart calendar with online booking and automated reminders\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white p-6 rounded-lg shadow-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4 mx-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-purple-600\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"Treatment Records\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Digital dental charts and comprehensive treatment documentation\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white p-6 rounded-lg shadow-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-4 mx-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-yellow-600\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M9 21V9l3-2 3 2v12\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"Inventory Management\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Smart stock tracking with automated alerts and usage analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white p-6 rounded-lg shadow-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4 mx-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-red-600\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"e-Prescriptions\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Digital prescriptions with QR codes and patient access\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white p-6 rounded-lg shadow-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4 mx-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6 text-indigo-600\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"WhatsApp Integration\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Automated reminders and patient communication via WhatsApp\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-16 bg-white rounded-lg shadow-lg p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                    children: \"Ready to Transform Your Practice?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-6\",\n                                    children: \"Start your 7-day free trial today. No credit card required.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/auth/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                size: \"lg\",\n                                                className: \"px-8\",\n                                                children: \"Start Free Trial\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/demo\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: \"outline\",\n                                                size: \"lg\",\n                                                className: \"px-8\",\n                                                children: \"View Demo\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-50 border-t mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-500\",\n                                children: _lib_constants__WEBPACK_IMPORTED_MODULE_2__.LICENSE_CONFIG.SOFTWARE_WATERMARK\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-500\",\n                                children: \"Built with Next.js & Tailwind CSS\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(rsc)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 45,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),
/* harmony export */   useTheme: () => (/* binding */ useTheme)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\contexts\\ThemeContext.tsx",
"ThemeProvider",
);const useTheme = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\contexts\\ThemeContext.tsx",
"useTheme",
);

/***/ }),

/***/ "(rsc)/./src/hooks/useAuth.tsx":
/*!*******************************!*\
  !*** ./src/hooks/useAuth.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\hooks\\useAuth.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\FAWZY\\New folder (3)\\dentopro-saas\\src\\hooks\\useAuth.tsx",
"useAuth",
);

/***/ }),

/***/ "(rsc)/./src/lib/constants.ts":
/*!******************************!*\
  !*** ./src/lib/constants.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_ENDPOINTS: () => (/* binding */ API_ENDPOINTS),\n/* harmony export */   APPOINTMENT_TYPES: () => (/* binding */ APPOINTMENT_TYPES),\n/* harmony export */   APP_DESCRIPTION: () => (/* binding */ APP_DESCRIPTION),\n/* harmony export */   APP_NAME: () => (/* binding */ APP_NAME),\n/* harmony export */   APP_VERSION: () => (/* binding */ APP_VERSION),\n/* harmony export */   DEFAULT_WORKING_HOURS: () => (/* binding */ DEFAULT_WORKING_HOURS),\n/* harmony export */   DENTAL_CHART: () => (/* binding */ DENTAL_CHART),\n/* harmony export */   ERROR_MESSAGES: () => (/* binding */ ERROR_MESSAGES),\n/* harmony export */   FILE_UPLOAD_CONFIG: () => (/* binding */ FILE_UPLOAD_CONFIG),\n/* harmony export */   LICENSE_CONFIG: () => (/* binding */ LICENSE_CONFIG),\n/* harmony export */   MATERIAL_CATEGORIES: () => (/* binding */ MATERIAL_CATEGORIES),\n/* harmony export */   NOTIFICATION_TEMPLATES: () => (/* binding */ NOTIFICATION_TEMPLATES),\n/* harmony export */   PAGINATION_CONFIG: () => (/* binding */ PAGINATION_CONFIG),\n/* harmony export */   SECURITY_CONFIG: () => (/* binding */ SECURITY_CONFIG),\n/* harmony export */   SUBSCRIPTION_PLANS: () => (/* binding */ SUBSCRIPTION_PLANS),\n/* harmony export */   SUCCESS_MESSAGES: () => (/* binding */ SUCCESS_MESSAGES),\n/* harmony export */   SUPPORTED_CURRENCIES: () => (/* binding */ SUPPORTED_CURRENCIES),\n/* harmony export */   SUPPORTED_LANGUAGES: () => (/* binding */ SUPPORTED_LANGUAGES),\n/* harmony export */   TRIAL_FEATURES: () => (/* binding */ TRIAL_FEATURES),\n/* harmony export */   TRIAL_PERIOD_DAYS: () => (/* binding */ TRIAL_PERIOD_DAYS)\n/* harmony export */ });\n// Application Constants\nconst APP_NAME = 'DentoPro';\nconst APP_VERSION = '1.0.0';\nconst APP_DESCRIPTION = 'Complete dental clinic management SaaS platform';\n// Subscription Plans\nconst SUBSCRIPTION_PLANS = {\n    BASIC: {\n        id: 'basic',\n        name: 'Basic Plan',\n        price: 9,\n        currency: 'USD',\n        interval: 'month',\n        features: [\n            'Up to 100 patients',\n            'Basic appointment scheduling',\n            'Treatment records',\n            'Basic invoicing',\n            'Email support',\n            'Mobile responsive'\n        ],\n        limits: {\n            patients: 100,\n            appointments: 500,\n            storage: '1GB'\n        }\n    },\n    PREMIUM: {\n        id: 'premium',\n        name: 'Premium Plan',\n        price: 15,\n        currency: 'USD',\n        interval: 'month',\n        features: [\n            'Unlimited patients',\n            'Advanced appointment scheduling',\n            'Complete treatment records',\n            'Advanced invoicing & payments',\n            'Inventory management',\n            'WhatsApp integration',\n            'Analytics & reports',\n            'Priority support',\n            'Mobile responsive'\n        ],\n        limits: {\n            patients: -1,\n            appointments: -1,\n            storage: '10GB'\n        }\n    }\n};\n// Trial Configuration\nconst TRIAL_PERIOD_DAYS = 7;\nconst TRIAL_FEATURES = SUBSCRIPTION_PLANS.PREMIUM.features;\n// Currency Configuration\nconst SUPPORTED_CURRENCIES = {\n    USD: {\n        code: 'USD',\n        symbol: '$',\n        name: 'US Dollar',\n        locale: 'en-US'\n    },\n    EGP: {\n        code: 'EGP',\n        symbol: 'ج.م',\n        name: 'Egyptian Pound',\n        locale: 'ar-EG'\n    },\n    EUR: {\n        code: 'EUR',\n        symbol: '€',\n        name: 'Euro',\n        locale: 'en-EU'\n    }\n};\n// Language Configuration\nconst SUPPORTED_LANGUAGES = {\n    en: {\n        code: 'en',\n        name: 'English',\n        nativeName: 'English',\n        direction: 'ltr'\n    },\n    ar: {\n        code: 'ar',\n        name: 'Arabic',\n        nativeName: 'العربية',\n        direction: 'rtl'\n    },\n    fr: {\n        code: 'fr',\n        name: 'French',\n        nativeName: 'Français',\n        direction: 'ltr'\n    }\n};\n// Appointment Configuration\nconst APPOINTMENT_TYPES = {\n    CONSULTATION: {\n        id: 'consultation',\n        name: 'Consultation',\n        duration: 30,\n        color: '#3B82F6'\n    },\n    CLEANING: {\n        id: 'cleaning',\n        name: 'Dental Cleaning',\n        duration: 45,\n        color: '#10B981'\n    },\n    FILLING: {\n        id: 'filling',\n        name: 'Filling',\n        duration: 60,\n        color: '#F59E0B'\n    },\n    ROOT_CANAL: {\n        id: 'root_canal',\n        name: 'Root Canal',\n        duration: 90,\n        color: '#EF4444'\n    },\n    EXTRACTION: {\n        id: 'extraction',\n        name: 'Tooth Extraction',\n        duration: 45,\n        color: '#8B5CF6'\n    },\n    CHECKUP: {\n        id: 'checkup',\n        name: 'Regular Checkup',\n        duration: 30,\n        color: '#06B6D4'\n    },\n    EMERGENCY: {\n        id: 'emergency',\n        name: 'Emergency',\n        duration: 60,\n        color: '#DC2626'\n    },\n    FOLLOW_UP: {\n        id: 'follow_up',\n        name: 'Follow-up',\n        duration: 30,\n        color: '#6B7280'\n    }\n};\n// Default Working Hours\nconst DEFAULT_WORKING_HOURS = {\n    monday: {\n        isOpen: true,\n        startTime: '09:00',\n        endTime: '17:00',\n        breakStart: '12:00',\n        breakEnd: '13:00'\n    },\n    tuesday: {\n        isOpen: true,\n        startTime: '09:00',\n        endTime: '17:00',\n        breakStart: '12:00',\n        breakEnd: '13:00'\n    },\n    wednesday: {\n        isOpen: true,\n        startTime: '09:00',\n        endTime: '17:00',\n        breakStart: '12:00',\n        breakEnd: '13:00'\n    },\n    thursday: {\n        isOpen: true,\n        startTime: '09:00',\n        endTime: '17:00',\n        breakStart: '12:00',\n        breakEnd: '13:00'\n    },\n    friday: {\n        isOpen: true,\n        startTime: '09:00',\n        endTime: '17:00',\n        breakStart: '12:00',\n        breakEnd: '13:00'\n    },\n    saturday: {\n        isOpen: true,\n        startTime: '09:00',\n        endTime: '14:00'\n    },\n    sunday: {\n        isOpen: false,\n        startTime: '',\n        endTime: ''\n    }\n};\n// Material Categories\nconst MATERIAL_CATEGORIES = {\n    FILLING: {\n        id: 'filling',\n        name: 'Filling Materials',\n        color: '#3B82F6'\n    },\n    ANESTHETIC: {\n        id: 'anesthetic',\n        name: 'Anesthetics',\n        color: '#EF4444'\n    },\n    CLEANING: {\n        id: 'cleaning',\n        name: 'Cleaning Supplies',\n        color: '#10B981'\n    },\n    SURGICAL: {\n        id: 'surgical',\n        name: 'Surgical Instruments',\n        color: '#8B5CF6'\n    },\n    IMPRESSION: {\n        id: 'impression',\n        name: 'Impression Materials',\n        color: '#F59E0B'\n    },\n    PROTECTIVE: {\n        id: 'protective',\n        name: 'Protective Equipment',\n        color: '#06B6D4'\n    },\n    OTHER: {\n        id: 'other',\n        name: 'Other',\n        color: '#6B7280'\n    }\n};\n// Dental Chart Configuration\nconst DENTAL_CHART = {\n    ADULT_TEETH: {\n        UPPER_RIGHT: [\n            18,\n            17,\n            16,\n            15,\n            14,\n            13,\n            12,\n            11\n        ],\n        UPPER_LEFT: [\n            21,\n            22,\n            23,\n            24,\n            25,\n            26,\n            27,\n            28\n        ],\n        LOWER_LEFT: [\n            31,\n            32,\n            33,\n            34,\n            35,\n            36,\n            37,\n            38\n        ],\n        LOWER_RIGHT: [\n            41,\n            42,\n            43,\n            44,\n            45,\n            46,\n            47,\n            48\n        ]\n    },\n    CHILD_TEETH: {\n        UPPER_RIGHT: [\n            55,\n            54,\n            53,\n            52,\n            51\n        ],\n        UPPER_LEFT: [\n            61,\n            62,\n            63,\n            64,\n            65\n        ],\n        LOWER_LEFT: [\n            71,\n            72,\n            73,\n            74,\n            75\n        ],\n        LOWER_RIGHT: [\n            81,\n            82,\n            83,\n            84,\n            85\n        ]\n    }\n};\n// Notification Templates\nconst NOTIFICATION_TEMPLATES = {\n    APPOINTMENT_REMINDER_24H: {\n        whatsapp: 'Hello {patientName}, this is a reminder that you have an appointment tomorrow at {time} with Dr. {doctorName} at {clinicName}. Please confirm your attendance.',\n        sms: 'Reminder: Appointment tomorrow at {time} with Dr. {doctorName}. Confirm: {confirmLink}',\n        email: {\n            subject: 'Appointment Reminder - {clinicName}',\n            body: 'Dear {patientName}, you have an appointment scheduled for tomorrow at {time}.'\n        }\n    },\n    APPOINTMENT_REMINDER_1H: {\n        whatsapp: 'Hello {patientName}, your appointment with Dr. {doctorName} is in 1 hour at {time}. See you soon!',\n        sms: 'Your appointment is in 1 hour at {time} with Dr. {doctorName}.',\n        email: {\n            subject: 'Appointment in 1 Hour - {clinicName}',\n            body: 'Your appointment is starting soon at {time}.'\n        }\n    },\n    DOCTOR_PATIENT_ARRIVAL: {\n        whatsapp: 'Dr. {doctorName}, your patient {patientName} should be arriving in 10 minutes for their {time} appointment.'\n    },\n    LOW_STOCK_ALERT: {\n        whatsapp: 'Dr. {doctorName}, {materialName} is running low. Current stock: {currentStock} {unit}. Threshold: {threshold} {unit}.',\n        email: {\n            subject: 'Low Stock Alert - {materialName}',\n            body: 'Your inventory item {materialName} is below the minimum threshold.'\n        }\n    }\n};\n// API Configuration\nconst API_ENDPOINTS = {\n    AUTH: {\n        LOGIN: '/api/auth/login',\n        LOGOUT: '/api/auth/logout',\n        REGISTER: '/api/auth/register',\n        VERIFY: '/api/auth/verify',\n        RESET_PASSWORD: '/api/auth/reset-password'\n    },\n    SUPER_ADMIN: {\n        DOCTORS: '/api/admin/doctors',\n        ANALYTICS: '/api/admin/analytics',\n        SUBSCRIPTIONS: '/api/admin/subscriptions',\n        LOGS: '/api/admin/logs',\n        SETTINGS: '/api/admin/settings'\n    },\n    DOCTOR: {\n        PROFILE: '/api/doctor/profile',\n        PATIENTS: '/api/doctor/patients',\n        APPOINTMENTS: '/api/doctor/appointments',\n        TREATMENTS: '/api/doctor/treatments',\n        INVOICES: '/api/doctor/invoices',\n        INVENTORY: '/api/doctor/inventory',\n        PRESCRIPTIONS: '/api/doctor/prescriptions',\n        SETTINGS: '/api/doctor/settings'\n    },\n    PATIENT: {\n        PROFILE: '/api/patient/profile',\n        APPOINTMENTS: '/api/patient/appointments',\n        TREATMENTS: '/api/patient/treatments',\n        INVOICES: '/api/patient/invoices',\n        PRESCRIPTIONS: '/api/patient/prescriptions'\n    },\n    PUBLIC: {\n        BOOK_APPOINTMENT: '/api/public/book-appointment',\n        AVAILABLE_SLOTS: '/api/public/available-slots',\n        CLINIC_INFO: '/api/public/clinic-info'\n    }\n};\n// Security Configuration\nconst SECURITY_CONFIG = {\n    PASSWORD_MIN_LENGTH: 8,\n    PASSWORD_REQUIRE_UPPERCASE: true,\n    PASSWORD_REQUIRE_LOWERCASE: true,\n    PASSWORD_REQUIRE_NUMBERS: true,\n    PASSWORD_REQUIRE_SYMBOLS: true,\n    SESSION_TIMEOUT_MINUTES: 60,\n    MAX_LOGIN_ATTEMPTS: 5,\n    LOCKOUT_DURATION_MINUTES: 30,\n    JWT_EXPIRY_HOURS: 24,\n    REFRESH_TOKEN_EXPIRY_DAYS: 30\n};\n// File Upload Configuration\nconst FILE_UPLOAD_CONFIG = {\n    MAX_FILE_SIZE: 10 * 1024 * 1024,\n    ALLOWED_IMAGE_TYPES: [\n        'image/jpeg',\n        'image/png',\n        'image/webp'\n    ],\n    ALLOWED_DOCUMENT_TYPES: [\n        'application/pdf',\n        'application/msword',\n        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n    ],\n    MAX_FILES_PER_UPLOAD: 5\n};\n// Pagination Configuration\nconst PAGINATION_CONFIG = {\n    DEFAULT_PAGE_SIZE: 20,\n    MAX_PAGE_SIZE: 100,\n    MIN_PAGE_SIZE: 5\n};\n// License Configuration\nconst LICENSE_CONFIG = {\n    SOFTWARE_WATERMARK: `© ${new Date().getFullYear()} DentoPro - Licensed to Single Doctor Use Only`,\n    DOMAIN_BINDING_REQUIRED: true,\n    MAX_CONCURRENT_SESSIONS: 3,\n    USAGE_TRACKING_ENABLED: true,\n    ANTI_RESALE_PROTECTION: true\n};\n// Error Messages\nconst ERROR_MESSAGES = {\n    UNAUTHORIZED: 'You are not authorized to access this resource',\n    FORBIDDEN: 'Access denied',\n    NOT_FOUND: 'Resource not found',\n    VALIDATION_ERROR: 'Validation failed',\n    SERVER_ERROR: 'Internal server error',\n    NETWORK_ERROR: 'Network connection error',\n    SUBSCRIPTION_EXPIRED: 'Your subscription has expired',\n    TRIAL_EXPIRED: 'Your trial period has ended',\n    LICENSE_VIOLATION: 'License violation detected',\n    DOMAIN_MISMATCH: 'Domain verification failed'\n};\n// Success Messages\nconst SUCCESS_MESSAGES = {\n    LOGIN_SUCCESS: 'Successfully logged in',\n    LOGOUT_SUCCESS: 'Successfully logged out',\n    REGISTRATION_SUCCESS: 'Registration completed successfully',\n    UPDATE_SUCCESS: 'Updated successfully',\n    DELETE_SUCCESS: 'Deleted successfully',\n    SAVE_SUCCESS: 'Saved successfully',\n    EMAIL_SENT: 'Email sent successfully',\n    APPOINTMENT_BOOKED: 'Appointment booked successfully',\n    PAYMENT_SUCCESS: 'Payment processed successfully'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/constants.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrayUtils: () => (/* binding */ arrayUtils),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   colorUtils: () => (/* binding */ colorUtils),\n/* harmony export */   currencyUtils: () => (/* binding */ currencyUtils),\n/* harmony export */   dateUtils: () => (/* binding */ dateUtils),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   errorUtils: () => (/* binding */ errorUtils),\n/* harmony export */   fileUtils: () => (/* binding */ fileUtils),\n/* harmony export */   storageUtils: () => (/* binding */ storageUtils),\n/* harmony export */   stringUtils: () => (/* binding */ stringUtils),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   urlUtils: () => (/* binding */ urlUtils),\n/* harmony export */   validationUtils: () => (/* binding */ validationUtils)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(rsc)/./src/lib/constants.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types */ \"(rsc)/./src/types/index.ts\");\n\n\n\n\n// Utility function for merging Tailwind classes\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_3__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// Date and Time Utilities\nconst dateUtils = {\n    formatDate: (date, locale = 'en-US')=>{\n        const dateObj = typeof date === 'string' ? new Date(date) : date;\n        return dateObj.toLocaleDateString(locale, {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    },\n    formatTime: (date, locale = 'en-US')=>{\n        const dateObj = typeof date === 'string' ? new Date(date) : date;\n        return dateObj.toLocaleTimeString(locale, {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    },\n    formatDateTime: (date, locale = 'en-US')=>{\n        const dateObj = typeof date === 'string' ? new Date(date) : date;\n        return dateObj.toLocaleString(locale, {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    },\n    isToday: (date)=>{\n        const dateObj = typeof date === 'string' ? new Date(date) : date;\n        const today = new Date();\n        return dateObj.toDateString() === today.toDateString();\n    },\n    isTomorrow: (date)=>{\n        const dateObj = typeof date === 'string' ? new Date(date) : date;\n        const tomorrow = new Date();\n        tomorrow.setDate(tomorrow.getDate() + 1);\n        return dateObj.toDateString() === tomorrow.toDateString();\n    },\n    addDays: (date, days)=>{\n        const result = new Date(date);\n        result.setDate(result.getDate() + days);\n        return result;\n    },\n    addMinutes: (date, minutes)=>{\n        const result = new Date(date);\n        result.setMinutes(result.getMinutes() + minutes);\n        return result;\n    },\n    getWeekStart: (date)=>{\n        const result = new Date(date);\n        const day = result.getDay();\n        const diff = result.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday\n        return new Date(result.setDate(diff));\n    },\n    getWeekEnd: (date)=>{\n        const weekStart = dateUtils.getWeekStart(date);\n        return dateUtils.addDays(weekStart, 6);\n    },\n    getMonthStart: (date)=>{\n        return new Date(date.getFullYear(), date.getMonth(), 1);\n    },\n    getMonthEnd: (date)=>{\n        return new Date(date.getFullYear(), date.getMonth() + 1, 0);\n    }\n};\n// Currency Utilities\nconst currencyUtils = {\n    format: (amount, currency = _types__WEBPACK_IMPORTED_MODULE_2__.Currency.USD)=>{\n        const currencyConfig = _constants__WEBPACK_IMPORTED_MODULE_1__.SUPPORTED_CURRENCIES[currency];\n        return new Intl.NumberFormat(currencyConfig.locale, {\n            style: 'currency',\n            currency: currencyConfig.code\n        }).format(amount);\n    },\n    formatWithSymbol: (amount, currency = _types__WEBPACK_IMPORTED_MODULE_2__.Currency.USD)=>{\n        const currencyConfig = _constants__WEBPACK_IMPORTED_MODULE_1__.SUPPORTED_CURRENCIES[currency];\n        return `${currencyConfig.symbol}${amount.toFixed(2)}`;\n    },\n    parse: (value)=>{\n        const numericValue = value.replace(/[^0-9.-]+/g, '');\n        return parseFloat(numericValue) || 0;\n    }\n};\n// String Utilities\nconst stringUtils = {\n    capitalize: (str)=>{\n        return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n    },\n    capitalizeWords: (str)=>{\n        return str.replace(/\\w\\S*/g, (txt)=>txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());\n    },\n    truncate: (str, length)=>{\n        return str.length > length ? str.substring(0, length) + '...' : str;\n    },\n    slugify: (str)=>{\n        return str.toLowerCase().replace(/[^\\w ]+/g, '').replace(/ +/g, '-');\n    },\n    generateId: (prefix = '')=>{\n        const timestamp = Date.now().toString(36);\n        const randomStr = Math.random().toString(36).substring(2, 8);\n        return `${prefix}${prefix ? '_' : ''}${timestamp}_${randomStr}`;\n    },\n    maskEmail: (email)=>{\n        const [username, domain] = email.split('@');\n        const maskedUsername = username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1);\n        return `${maskedUsername}@${domain}`;\n    },\n    maskPhone: (phone)=>{\n        const cleaned = phone.replace(/\\D/g, '');\n        if (cleaned.length >= 10) {\n            return cleaned.replace(/(\\d{3})\\d{4}(\\d{3})/, '$1****$2');\n        }\n        return phone;\n    }\n};\n// Validation Utilities\nconst validationUtils = {\n    isEmail: (email)=>{\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        return emailRegex.test(email);\n    },\n    isPhone: (phone)=>{\n        const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]{10,}$/;\n        return phoneRegex.test(phone);\n    },\n    isStrongPassword: (password)=>{\n        const minLength = 8;\n        const hasUpperCase = /[A-Z]/.test(password);\n        const hasLowerCase = /[a-z]/.test(password);\n        const hasNumbers = /\\d/.test(password);\n        const hasSymbols = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\n        return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSymbols;\n    },\n    isValidDate: (date)=>{\n        const dateObj = new Date(date);\n        return dateObj instanceof Date && !isNaN(dateObj.getTime());\n    },\n    isValidTime: (time)=>{\n        const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;\n        return timeRegex.test(time);\n    }\n};\n// Array Utilities\nconst arrayUtils = {\n    chunk: (array, size)=>{\n        const chunks = [];\n        for(let i = 0; i < array.length; i += size){\n            chunks.push(array.slice(i, i + size));\n        }\n        return chunks;\n    },\n    unique: (array)=>{\n        return [\n            ...new Set(array)\n        ];\n    },\n    groupBy: (array, key)=>{\n        return array.reduce((groups, item)=>{\n            const group = String(item[key]);\n            groups[group] = groups[group] || [];\n            groups[group].push(item);\n            return groups;\n        }, {});\n    },\n    sortBy: (array, key, direction = 'asc')=>{\n        return [\n            ...array\n        ].sort((a, b)=>{\n            const aVal = a[key];\n            const bVal = b[key];\n            if (aVal < bVal) return direction === 'asc' ? -1 : 1;\n            if (aVal > bVal) return direction === 'asc' ? 1 : -1;\n            return 0;\n        });\n    }\n};\n// Local Storage Utilities\nconst storageUtils = {\n    set: (key, value)=>{\n        if (false) {}\n    },\n    get: (key, defaultValue)=>{\n        if (false) {}\n        return defaultValue || null;\n    },\n    remove: (key)=>{\n        if (false) {}\n    },\n    clear: ()=>{\n        if (false) {}\n    }\n};\n// URL Utilities\nconst urlUtils = {\n    buildQuery: (params)=>{\n        const searchParams = new URLSearchParams();\n        Object.entries(params).forEach(([key, value])=>{\n            if (value !== undefined && value !== null && value !== '') {\n                searchParams.append(key, String(value));\n            }\n        });\n        return searchParams.toString();\n    },\n    parseQuery: (search)=>{\n        const params = new URLSearchParams(search);\n        const result = {};\n        params.forEach((value, key)=>{\n            result[key] = value;\n        });\n        return result;\n    }\n};\n// File Utilities\nconst fileUtils = {\n    formatFileSize: (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    },\n    getFileExtension: (filename)=>{\n        return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);\n    },\n    isImageFile: (filename)=>{\n        const imageExtensions = [\n            'jpg',\n            'jpeg',\n            'png',\n            'gif',\n            'webp',\n            'svg'\n        ];\n        const extension = fileUtils.getFileExtension(filename).toLowerCase();\n        return imageExtensions.includes(extension);\n    },\n    isPdfFile: (filename)=>{\n        return fileUtils.getFileExtension(filename).toLowerCase() === 'pdf';\n    }\n};\n// Color Utilities\nconst colorUtils = {\n    hexToRgb: (hex)=>{\n        const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n        return result ? {\n            r: parseInt(result[1], 16),\n            g: parseInt(result[2], 16),\n            b: parseInt(result[3], 16)\n        } : null;\n    },\n    rgbToHex: (r, g, b)=>{\n        return \"#\" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);\n    },\n    getContrastColor: (hexColor)=>{\n        const rgb = colorUtils.hexToRgb(hexColor);\n        if (!rgb) return '#000000';\n        const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n        return brightness > 128 ? '#000000' : '#ffffff';\n    }\n};\n// Error Handling Utilities\nconst errorUtils = {\n    getErrorMessage: (error)=>{\n        if (error instanceof Error) {\n            return error.message;\n        }\n        if (typeof error === 'string') {\n            return error;\n        }\n        return 'An unknown error occurred';\n    },\n    isNetworkError: (error)=>{\n        return error instanceof Error && (error.message.includes('fetch') || error.message.includes('network') || error.message.includes('connection'));\n    }\n};\n// Debounce Utility\nconst debounce = (func, wait)=>{\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n};\n// Throttle Utility\nconst throttle = (func, limit)=>{\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppointmentSource: () => (/* binding */ AppointmentSource),\n/* harmony export */   AppointmentStatus: () => (/* binding */ AppointmentStatus),\n/* harmony export */   AppointmentType: () => (/* binding */ AppointmentType),\n/* harmony export */   Currency: () => (/* binding */ Currency),\n/* harmony export */   Gender: () => (/* binding */ Gender),\n/* harmony export */   InvoiceStatus: () => (/* binding */ InvoiceStatus),\n/* harmony export */   Language: () => (/* binding */ Language),\n/* harmony export */   LogLevel: () => (/* binding */ LogLevel),\n/* harmony export */   MaterialCategory: () => (/* binding */ MaterialCategory),\n/* harmony export */   MaterialLogType: () => (/* binding */ MaterialLogType),\n/* harmony export */   NotificationStatus: () => (/* binding */ NotificationStatus),\n/* harmony export */   NotificationType: () => (/* binding */ NotificationType),\n/* harmony export */   PaymentMethod: () => (/* binding */ PaymentMethod),\n/* harmony export */   SubscriptionPlan: () => (/* binding */ SubscriptionPlan),\n/* harmony export */   SubscriptionStatus: () => (/* binding */ SubscriptionStatus),\n/* harmony export */   UserRole: () => (/* binding */ UserRole),\n/* harmony export */   ViolationType: () => (/* binding */ ViolationType)\n/* harmony export */ });\n// Core User Types\nvar UserRole = /*#__PURE__*/ function(UserRole) {\n    UserRole[\"SUPER_ADMIN\"] = \"super_admin\";\n    UserRole[\"DOCTOR\"] = \"doctor\";\n    UserRole[\"PATIENT\"] = \"patient\";\n    return UserRole;\n}({});\nvar SubscriptionPlan = /*#__PURE__*/ function(SubscriptionPlan) {\n    SubscriptionPlan[\"BASIC\"] = \"basic\";\n    SubscriptionPlan[\"PREMIUM\"] = \"premium\";\n    return SubscriptionPlan;\n}({});\nvar SubscriptionStatus = /*#__PURE__*/ function(SubscriptionStatus) {\n    SubscriptionStatus[\"TRIAL\"] = \"trial\";\n    SubscriptionStatus[\"ACTIVE\"] = \"active\";\n    SubscriptionStatus[\"SUSPENDED\"] = \"suspended\";\n    SubscriptionStatus[\"CANCELLED\"] = \"cancelled\";\n    return SubscriptionStatus;\n}({});\nvar Language = /*#__PURE__*/ function(Language) {\n    Language[\"ENGLISH\"] = \"en\";\n    Language[\"ARABIC\"] = \"ar\";\n    Language[\"FRENCH\"] = \"fr\";\n    return Language;\n}({});\nvar Currency = /*#__PURE__*/ function(Currency) {\n    Currency[\"USD\"] = \"USD\";\n    Currency[\"EGP\"] = \"EGP\";\n    Currency[\"EUR\"] = \"EUR\";\n    return Currency;\n}({});\nvar Gender = /*#__PURE__*/ function(Gender) {\n    Gender[\"MALE\"] = \"male\";\n    Gender[\"FEMALE\"] = \"female\";\n    Gender[\"OTHER\"] = \"other\";\n    return Gender;\n}({});\nvar AppointmentType = /*#__PURE__*/ function(AppointmentType) {\n    AppointmentType[\"CONSULTATION\"] = \"consultation\";\n    AppointmentType[\"CLEANING\"] = \"cleaning\";\n    AppointmentType[\"FILLING\"] = \"filling\";\n    AppointmentType[\"ROOT_CANAL\"] = \"root_canal\";\n    AppointmentType[\"EXTRACTION\"] = \"extraction\";\n    AppointmentType[\"CHECKUP\"] = \"checkup\";\n    AppointmentType[\"EMERGENCY\"] = \"emergency\";\n    AppointmentType[\"FOLLOW_UP\"] = \"follow_up\";\n    return AppointmentType;\n}({});\nvar AppointmentStatus = /*#__PURE__*/ function(AppointmentStatus) {\n    AppointmentStatus[\"SCHEDULED\"] = \"scheduled\";\n    AppointmentStatus[\"CONFIRMED\"] = \"confirmed\";\n    AppointmentStatus[\"IN_PROGRESS\"] = \"in_progress\";\n    AppointmentStatus[\"COMPLETED\"] = \"completed\";\n    AppointmentStatus[\"CANCELLED\"] = \"cancelled\";\n    AppointmentStatus[\"NO_SHOW\"] = \"no_show\";\n    return AppointmentStatus;\n}({});\nvar AppointmentSource = /*#__PURE__*/ function(AppointmentSource) {\n    AppointmentSource[\"DOCTOR\"] = \"doctor\";\n    AppointmentSource[\"PATIENT_ONLINE\"] = \"patient_online\";\n    AppointmentSource[\"PHONE\"] = \"phone\";\n    AppointmentSource[\"WALK_IN\"] = \"walk_in\";\n    return AppointmentSource;\n}({});\nvar MaterialCategory = /*#__PURE__*/ function(MaterialCategory) {\n    MaterialCategory[\"FILLING\"] = \"filling\";\n    MaterialCategory[\"ANESTHETIC\"] = \"anesthetic\";\n    MaterialCategory[\"CLEANING\"] = \"cleaning\";\n    MaterialCategory[\"SURGICAL\"] = \"surgical\";\n    MaterialCategory[\"IMPRESSION\"] = \"impression\";\n    MaterialCategory[\"PROTECTIVE\"] = \"protective\";\n    MaterialCategory[\"OTHER\"] = \"other\";\n    return MaterialCategory;\n}({});\nvar MaterialLogType = /*#__PURE__*/ function(MaterialLogType) {\n    MaterialLogType[\"STOCK_IN\"] = \"stock_in\";\n    MaterialLogType[\"STOCK_OUT\"] = \"stock_out\";\n    MaterialLogType[\"ADJUSTMENT\"] = \"adjustment\";\n    MaterialLogType[\"EXPIRED\"] = \"expired\";\n    MaterialLogType[\"DAMAGED\"] = \"damaged\";\n    return MaterialLogType;\n}({});\nvar InvoiceStatus = /*#__PURE__*/ function(InvoiceStatus) {\n    InvoiceStatus[\"DRAFT\"] = \"draft\";\n    InvoiceStatus[\"SENT\"] = \"sent\";\n    InvoiceStatus[\"PAID\"] = \"paid\";\n    InvoiceStatus[\"OVERDUE\"] = \"overdue\";\n    InvoiceStatus[\"CANCELLED\"] = \"cancelled\";\n    return InvoiceStatus;\n}({});\nvar PaymentMethod = /*#__PURE__*/ function(PaymentMethod) {\n    PaymentMethod[\"CASH\"] = \"cash\";\n    PaymentMethod[\"CARD\"] = \"card\";\n    PaymentMethod[\"BANK_TRANSFER\"] = \"bank_transfer\";\n    PaymentMethod[\"INSURANCE\"] = \"insurance\";\n    PaymentMethod[\"ONLINE\"] = \"online\";\n    return PaymentMethod;\n}({});\nvar ViolationType = /*#__PURE__*/ function(ViolationType) {\n    ViolationType[\"DOMAIN_MISMATCH\"] = \"domain_mismatch\";\n    ViolationType[\"MULTIPLE_INSTANCES\"] = \"multiple_instances\";\n    ViolationType[\"UNAUTHORIZED_ACCESS\"] = \"unauthorized_access\";\n    ViolationType[\"RESALE_ATTEMPT\"] = \"resale_attempt\";\n    return ViolationType;\n}({});\nvar LogLevel = /*#__PURE__*/ function(LogLevel) {\n    LogLevel[\"INFO\"] = \"info\";\n    LogLevel[\"WARN\"] = \"warn\";\n    LogLevel[\"ERROR\"] = \"error\";\n    LogLevel[\"DEBUG\"] = \"debug\";\n    return LogLevel;\n}({});\nvar NotificationType = /*#__PURE__*/ function(NotificationType) {\n    NotificationType[\"APPOINTMENT_REMINDER\"] = \"appointment_reminder\";\n    NotificationType[\"LOW_STOCK\"] = \"low_stock\";\n    NotificationType[\"PAYMENT_DUE\"] = \"payment_due\";\n    NotificationType[\"SYSTEM_ALERT\"] = \"system_alert\";\n    NotificationType[\"NEW_PATIENT\"] = \"new_patient\";\n    NotificationType[\"SUBSCRIPTION_EXPIRY\"] = \"subscription_expiry\";\n    return NotificationType;\n}({});\nvar NotificationStatus = /*#__PURE__*/ function(NotificationStatus) {\n    NotificationStatus[\"PENDING\"] = \"pending\";\n    NotificationStatus[\"SENT\"] = \"sent\";\n    NotificationStatus[\"DELIVERED\"] = \"delivered\";\n    NotificationStatus[\"READ\"] = \"read\";\n    NotificationStatus[\"FAILED\"] = \"failed\";\n    return NotificationStatus;\n}({});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/types/index.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNGQVdaWSU1QyU1Q05ldyUyMGZvbGRlciUyMCgzKSU1QyU1Q2RlbnRvcHJvLXNhYXMlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2FwcC1kaXIlNUMlNUNsaW5rLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyX19lc01vZHVsZSUyMiUyQyUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdOQUEySyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiX19lc01vZHVsZVwiLFwiZGVmYXVsdFwiXSAqLyBcIkQ6XFxcXEZBV1pZXFxcXE5ldyBmb2xkZXIgKDMpXFxcXGRlbnRvcHJvLXNhYXNcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcYXBwLWRpclxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Csrc%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Csrc%5C%5Chooks%5C%5CuseAuth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Csrc%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Csrc%5C%5Chooks%5C%5CuseAuth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/ThemeContext.tsx */ \"(ssr)/./src/contexts/ThemeContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/hooks/useAuth.tsx */ \"(ssr)/./src/hooks/useAuth.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNGQVdaWSU1QyU1Q05ldyUyMGZvbGRlciUyMCgzKSU1QyU1Q2RlbnRvcHJvLXNhYXMlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDRkFXWlklNUMlNUNOZXclMjBmb2xkZXIlMjAoMyklNUMlNUNkZW50b3Byby1zYWFzJTVDJTVDc3JjJTVDJTVDY29udGV4dHMlNUMlNUNUaGVtZUNvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVGhlbWVQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDRkFXWlklNUMlNUNOZXclMjBmb2xkZXIlMjAoMyklNUMlNUNkZW50b3Byby1zYWFzJTVDJTVDc3JjJTVDJTVDaG9va3MlNUMlNUN1c2VBdXRoLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEtBQWlKO0FBQ2pKO0FBQ0EsMEpBQXdJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUaGVtZVByb3ZpZGVyXCJdICovIFwiRDpcXFxcRkFXWllcXFxcTmV3IGZvbGRlciAoMylcXFxcZGVudG9wcm8tc2Fhc1xcXFxzcmNcXFxcY29udGV4dHNcXFxcVGhlbWVDb250ZXh0LnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiRDpcXFxcRkFXWllcXFxcTmV3IGZvbGRlciAoMylcXFxcZGVudG9wcm8tc2Fhc1xcXFxzcmNcXFxcaG9va3NcXFxcdXNlQXV0aC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Csrc%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CFAWZY%5C%5CNew%20folder%20(3)%5C%5Cdentopro-saas%5C%5Csrc%5C%5Chooks%5C%5CuseAuth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst initialState = {\n    theme: 'system',\n    setTheme: ()=>null\n};\nconst ThemeProviderContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(initialState);\nfunction ThemeProvider({ children, defaultTheme = 'system', storageKey = 'dentopro-ui-theme', ...props }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ThemeProvider.useState\": ()=>{\n            // Check if we're in the browser\n            if (true) {\n                return defaultTheme;\n            }\n            try {\n                return localStorage?.getItem(storageKey) || defaultTheme;\n            } catch  {\n                return defaultTheme;\n            }\n        }\n    }[\"ThemeProvider.useState\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            // Check if we're in the browser\n            if (true) return;\n            const root = window.document.documentElement;\n            root.classList.remove('light', 'dark');\n            if (theme === 'system') {\n                const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n                root.classList.add(systemTheme);\n                return;\n            }\n            root.classList.add(theme);\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme\n    ]);\n    const value = {\n        theme,\n        setTheme: (theme)=>{\n            // Check if we're in the browser before accessing localStorage\n            if (false) {}\n            setTheme(theme);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeProviderContext.Provider, {\n        ...props,\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\nconst useTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeProviderContext);\n    if (context === undefined) throw new Error('useTheme must be used within a ThemeProvider');\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAuth.tsx":
/*!*******************************!*\
  !*** ./src/hooks/useAuth.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types */ \"(ssr)/./src/types/index.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [doctorId, setDoctorId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [patientId, setPatientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [is2FAEnabled, setIs2FAEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.createClientSupabaseClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Get initial session\n            const getInitialSession = {\n                \"AuthProvider.useEffect.getInitialSession\": async ()=>{\n                    const { data: { session }, error } = await supabase.auth.getSession();\n                    if (error) {\n                        console.error('Error getting session:', error);\n                    } else {\n                        await handleAuthStateChange(session);\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.getInitialSession\"];\n            getInitialSession();\n            // Listen for auth state changes\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    console.log('Auth state changed:', event, session?.user?.email);\n                    await handleAuthStateChange(session);\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    subscription.unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        supabase.auth\n    ]);\n    const handleAuthStateChange = async (session)=>{\n        setSession(session);\n        if (session?.user) {\n            // Convert Supabase user to our User type\n            const userRole = await getUserRole(session.user);\n            const userWithRole = {\n                id: session.user.id,\n                email: session.user.email || '',\n                role: userRole,\n                createdAt: new Date(session.user.created_at),\n                updatedAt: new Date(session.user.updated_at || session.user.created_at)\n            };\n            setUser(userWithRole);\n            // Set role-specific IDs\n            if (userRole === _types__WEBPACK_IMPORTED_MODULE_2__.UserRole.DOCTOR) {\n                setDoctorId(session.user.id);\n                setPatientId(null);\n            } else if (userRole === _types__WEBPACK_IMPORTED_MODULE_2__.UserRole.PATIENT) {\n                setPatientId(session.user.id);\n                const doctorId = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.SupabaseUtils.getCurrentDoctorId();\n                setDoctorId(doctorId);\n            } else {\n                setDoctorId(null);\n                setPatientId(null);\n            }\n        } else {\n            setUser(null);\n            setDoctorId(null);\n            setPatientId(null);\n        }\n    };\n    const getUserRole = async (supabaseUser)=>{\n        // Check user metadata first\n        const metadataRole = supabaseUser.user_metadata?.role || supabaseUser.app_metadata?.role;\n        if (metadataRole) {\n            return metadataRole;\n        }\n        // Check if user exists in platform_admins table\n        try {\n            const { data: admin } = await supabase.from('platform_admins').select('id').eq('id', supabaseUser.id).single();\n            if (admin) {\n                return _types__WEBPACK_IMPORTED_MODULE_2__.UserRole.SUPER_ADMIN;\n            }\n        } catch (error) {\n        // User not found in platform_admins, continue checking\n        }\n        // Check if user exists in doctors table\n        try {\n            const { data: doctor } = await supabase.from('doctors').select('id').eq('id', supabaseUser.id).single();\n            if (doctor) {\n                return _types__WEBPACK_IMPORTED_MODULE_2__.UserRole.DOCTOR;\n            }\n        } catch (error) {\n        // User not found in doctors, continue checking\n        }\n        // Check if user exists in patients table\n        try {\n            const { data: patient } = await supabase.from('patients').select('id').eq('id', supabaseUser.id).single();\n            if (patient) {\n                return _types__WEBPACK_IMPORTED_MODULE_2__.UserRole.PATIENT;\n            }\n        } catch (error) {\n        // User not found in patients\n        }\n        // Default to patient role for new users\n        return _types__WEBPACK_IMPORTED_MODULE_2__.UserRole.PATIENT;\n    };\n    const login = async (email, password)=>{\n        setLoading(true);\n        try {\n            const result = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.SupabaseUtils.signInWithPassword(email, password);\n            if (result.error) {\n                throw result.error;\n            }\n            return result;\n        } catch (error) {\n            console.error('Login failed:', error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loginWithGoogle = async ()=>{\n        setLoading(true);\n        try {\n            const { data, error } = await supabase.auth.signInWithOAuth({\n                provider: 'google',\n                options: {\n                    redirectTo: `${window.location.origin}/auth/callback`,\n                    queryParams: {\n                        access_type: 'offline',\n                        prompt: 'consent'\n                    }\n                }\n            });\n            if (error) {\n                throw error;\n            }\n            return {\n                data,\n                error: null\n            };\n        } catch (error) {\n            console.error('Google login failed:', error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loginWithGitHub = async ()=>{\n        setLoading(true);\n        try {\n            const { data, error } = await supabase.auth.signInWithOAuth({\n                provider: 'github',\n                options: {\n                    redirectTo: `${window.location.origin}/auth/callback`\n                }\n            });\n            if (error) {\n                throw error;\n            }\n            return {\n                data,\n                error: null\n            };\n        } catch (error) {\n            console.error('GitHub login failed:', error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loginWithMicrosoft = async ()=>{\n        setLoading(true);\n        try {\n            const { data, error } = await supabase.auth.signInWithOAuth({\n                provider: 'azure',\n                options: {\n                    redirectTo: `${window.location.origin}/auth/callback`,\n                    scopes: 'email profile'\n                }\n            });\n            if (error) {\n                throw error;\n            }\n            return {\n                data,\n                error: null\n            };\n        } catch (error) {\n            console.error('Microsoft login failed:', error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        setLoading(true);\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.SupabaseUtils.signOut();\n            if (error) {\n                throw error;\n            }\n        } catch (error) {\n            console.error('Logout failed:', error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const register = async (userData)=>{\n        setLoading(true);\n        try {\n            const result = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.SupabaseUtils.signUp(userData.email, userData.password, {\n                role: userData.role,\n                ...userData.metadata\n            });\n            if (result.error) {\n                throw result.error;\n            }\n            return result;\n        } catch (error) {\n            console.error('Registration failed:', error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const resetPassword = async (email)=>{\n        try {\n            const result = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.SupabaseUtils.resetPassword(email);\n            if (result.error) {\n                throw result.error;\n            }\n            return result;\n        } catch (error) {\n            console.error('Password reset failed:', error);\n            throw error;\n        }\n    };\n    // Two-Factor Authentication Functions\n    const enable2FA = async (phoneNumber)=>{\n        try {\n            if (!user) {\n                throw new Error('User must be logged in to enable 2FA');\n            }\n            // Generate and send verification code\n            const response = await fetch('/api/auth/2fa/enable', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    userId: user.id,\n                    phoneNumber: phoneNumber\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Failed to enable 2FA');\n            }\n            return result;\n        } catch (error) {\n            console.error('2FA enable failed:', error);\n            throw error;\n        }\n    };\n    const verify2FA = async (code)=>{\n        try {\n            if (!user) {\n                throw new Error('User must be logged in to verify 2FA');\n            }\n            const response = await fetch('/api/auth/2fa/verify', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    userId: user.id,\n                    code: code\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Failed to verify 2FA code');\n            }\n            // Update 2FA status\n            setIs2FAEnabled(result.enabled);\n            return result;\n        } catch (error) {\n            console.error('2FA verification failed:', error);\n            throw error;\n        }\n    };\n    const disable2FA = async ()=>{\n        try {\n            if (!user) {\n                throw new Error('User must be logged in to disable 2FA');\n            }\n            const response = await fetch('/api/auth/2fa/disable', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    userId: user.id\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'Failed to disable 2FA');\n            }\n            // Update 2FA status\n            setIs2FAEnabled(false);\n            return result;\n        } catch (error) {\n            console.error('2FA disable failed:', error);\n            throw error;\n        }\n    };\n    const hasRole = (role)=>{\n        return user?.role === role;\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        login,\n        loginWithGoogle,\n        loginWithGitHub,\n        loginWithMicrosoft,\n        logout,\n        register,\n        resetPassword,\n        enable2FA,\n        verify2FA,\n        disable2FA,\n        is2FAEnabled,\n        isAuthenticated: !!user,\n        hasRole,\n        isSuperAdmin: hasRole(_types__WEBPACK_IMPORTED_MODULE_2__.UserRole.SUPER_ADMIN),\n        isDoctor: hasRole(_types__WEBPACK_IMPORTED_MODULE_2__.UserRole.DOCTOR),\n        isPatient: hasRole(_types__WEBPACK_IMPORTED_MODULE_2__.UserRole.PATIENT),\n        doctorId,\n        patientId\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\hooks\\\\useAuth.tsx\",\n        lineNumber: 434,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAuth.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseDatabase: () => (/* binding */ SupabaseDatabase),\n/* harmony export */   SupabaseUtils: () => (/* binding */ SupabaseUtils),\n/* harmony export */   createClientSupabaseClient: () => (/* binding */ createClientSupabaseClient),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n// Supabase client configuration for DentoPro SaaS platform\n\n\n// Environment variables validation\nconst supabaseUrl = \"https://mjurshygvtfntjipemin.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1qdXJzaHlndnRmbnRqaXBlbWluIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMxMDM0NTUsImV4cCI6MjA2ODY3OTQ1NX0.MeBN_BQMpAFPWzwVTvc2N2klfWislsPxwIscpdu071k\";\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error('Missing Supabase environment variables. Please check your .env file.');\n}\n// Client-side Supabase client (for browser)\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    }\n});\n// Server-side Supabase client with service role (for admin operations)\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceRoleKey || supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Note: Server component client is in supabase-server.ts to avoid Next.js import issues\n// Client component client (for Next.js client components)\nconst createClientSupabaseClient = ()=>{\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey);\n};\n// Supabase utilities\nclass SupabaseUtils {\n    // Get current user with role information\n    static async getCurrentUser() {\n        const supabase = createClientSupabaseClient();\n        const { data: { user }, error } = await supabase.auth.getUser();\n        if (error || !user) {\n            return null;\n        }\n        // Get user role from custom claims or user metadata\n        const role = user.user_metadata?.role || user.app_metadata?.role || 'patient';\n        return {\n            ...user,\n            role\n        };\n    }\n    // Check if user has specific role\n    static async hasRole(requiredRole) {\n        const user = await this.getCurrentUser();\n        return user?.role === requiredRole;\n    }\n    // Get doctor ID from current user\n    static async getCurrentDoctorId() {\n        const user = await this.getCurrentUser();\n        if (user?.role === 'doctor') {\n            return user.id;\n        }\n        if (user?.role === 'patient') {\n            // For patients, get their doctor_id from the patients table\n            const supabase = createClientSupabaseClient();\n            const { data: patient } = await supabase.from('patients').select('doctor_id').eq('id', user.id).single();\n            return patient?.doctor_id || null;\n        }\n        return null;\n    }\n    // Get patient ID from current user\n    static async getCurrentPatientId() {\n        const user = await this.getCurrentUser();\n        return user?.role === 'patient' ? user.id : null;\n    }\n    // Sign in with email and password\n    static async signInWithPassword(email, password) {\n        const supabase = createClientSupabaseClient();\n        return await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n    }\n    // Sign in with Google OAuth\n    static async signInWithGoogle() {\n        const supabase = createClientSupabaseClient();\n        return await supabase.auth.signInWithOAuth({\n            provider: 'google',\n            options: {\n                redirectTo: `${window.location.origin}/auth/callback`\n            }\n        });\n    }\n    // Sign up new user\n    static async signUp(email, password, metadata) {\n        const supabase = createClientSupabaseClient();\n        return await supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: metadata\n            }\n        });\n    }\n    // Sign out\n    static async signOut() {\n        const supabase = createClientSupabaseClient();\n        return await supabase.auth.signOut();\n    }\n    // Update user metadata\n    static async updateUserMetadata(metadata) {\n        const supabase = createClientSupabaseClient();\n        return await supabase.auth.updateUser({\n            data: metadata\n        });\n    }\n    // Reset password\n    static async resetPassword(email) {\n        const supabase = createClientSupabaseClient();\n        return await supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: `${window.location.origin}/auth/reset-password`\n        });\n    }\n}\n// Database query helpers with RLS\nclass SupabaseDatabase {\n    // Get data with automatic doctor filtering\n    async findMany(table, filters = {}, options = {}) {\n        let query = this.supabase.from(table).select(options.select || '*');\n        // Apply filters\n        Object.entries(filters).forEach(([key, value])=>{\n            query = query.eq(key, value);\n        });\n        // Apply ordering\n        if (options.orderBy) {\n            const [column, direction] = options.orderBy.split(' ');\n            query = query.order(column, {\n                ascending: direction !== 'desc'\n            });\n        }\n        // Apply pagination\n        if (options.limit) {\n            query = query.limit(options.limit);\n        }\n        if (options.offset) {\n            query = query.range(options.offset, options.offset + (options.limit || 10) - 1);\n        }\n        const { data, error } = await query;\n        if (error) {\n            console.error('Database query error:', error);\n            throw error;\n        }\n        return data;\n    }\n    // Find single record\n    async findOne(table, filters) {\n        let query = this.supabase.from(table).select('*');\n        Object.entries(filters).forEach(([key, value])=>{\n            query = query.eq(key, value);\n        });\n        const { data, error } = await query.single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Database query error:', error);\n            throw error;\n        }\n        return data;\n    }\n    // Insert record\n    async insert(table, data) {\n        const { data: result, error } = await this.supabase.from(table).insert(data).select().single();\n        if (error) {\n            console.error('Database insert error:', error);\n            throw error;\n        }\n        return result;\n    }\n    // Update record\n    async update(table, data, filters) {\n        let query = this.supabase.from(table).update(data);\n        Object.entries(filters).forEach(([key, value])=>{\n            query = query.eq(key, value);\n        });\n        const { data: result, error } = await query.select().single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Database update error:', error);\n            throw error;\n        }\n        return result;\n    }\n    // Delete record\n    async delete(table, filters) {\n        let query = this.supabase.from(table).delete();\n        Object.entries(filters).forEach(([key, value])=>{\n            query = query.eq(key, value);\n        });\n        const { error } = await query;\n        if (error) {\n            console.error('Database delete error:', error);\n            throw error;\n        }\n    }\n    // Count records\n    async count(table, filters = {}) {\n        let query = this.supabase.from(table).select('*', {\n            count: 'exact',\n            head: true\n        });\n        Object.entries(filters).forEach(([key, value])=>{\n            query = query.eq(key, value);\n        });\n        const { count, error } = await query;\n        if (error) {\n            console.error('Database count error:', error);\n            throw error;\n        }\n        return count || 0;\n    }\n    constructor(){\n        this.supabase = createClientSupabaseClient();\n    }\n}\n// Export database instance\nconst db = new SupabaseDatabase();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (supabase);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppointmentSource: () => (/* binding */ AppointmentSource),\n/* harmony export */   AppointmentStatus: () => (/* binding */ AppointmentStatus),\n/* harmony export */   AppointmentType: () => (/* binding */ AppointmentType),\n/* harmony export */   Currency: () => (/* binding */ Currency),\n/* harmony export */   Gender: () => (/* binding */ Gender),\n/* harmony export */   InvoiceStatus: () => (/* binding */ InvoiceStatus),\n/* harmony export */   Language: () => (/* binding */ Language),\n/* harmony export */   LogLevel: () => (/* binding */ LogLevel),\n/* harmony export */   MaterialCategory: () => (/* binding */ MaterialCategory),\n/* harmony export */   MaterialLogType: () => (/* binding */ MaterialLogType),\n/* harmony export */   NotificationStatus: () => (/* binding */ NotificationStatus),\n/* harmony export */   NotificationType: () => (/* binding */ NotificationType),\n/* harmony export */   PaymentMethod: () => (/* binding */ PaymentMethod),\n/* harmony export */   SubscriptionPlan: () => (/* binding */ SubscriptionPlan),\n/* harmony export */   SubscriptionStatus: () => (/* binding */ SubscriptionStatus),\n/* harmony export */   UserRole: () => (/* binding */ UserRole),\n/* harmony export */   ViolationType: () => (/* binding */ ViolationType)\n/* harmony export */ });\n// Core User Types\nvar UserRole = /*#__PURE__*/ function(UserRole) {\n    UserRole[\"SUPER_ADMIN\"] = \"super_admin\";\n    UserRole[\"DOCTOR\"] = \"doctor\";\n    UserRole[\"PATIENT\"] = \"patient\";\n    return UserRole;\n}({});\nvar SubscriptionPlan = /*#__PURE__*/ function(SubscriptionPlan) {\n    SubscriptionPlan[\"BASIC\"] = \"basic\";\n    SubscriptionPlan[\"PREMIUM\"] = \"premium\";\n    return SubscriptionPlan;\n}({});\nvar SubscriptionStatus = /*#__PURE__*/ function(SubscriptionStatus) {\n    SubscriptionStatus[\"TRIAL\"] = \"trial\";\n    SubscriptionStatus[\"ACTIVE\"] = \"active\";\n    SubscriptionStatus[\"SUSPENDED\"] = \"suspended\";\n    SubscriptionStatus[\"CANCELLED\"] = \"cancelled\";\n    return SubscriptionStatus;\n}({});\nvar Language = /*#__PURE__*/ function(Language) {\n    Language[\"ENGLISH\"] = \"en\";\n    Language[\"ARABIC\"] = \"ar\";\n    Language[\"FRENCH\"] = \"fr\";\n    return Language;\n}({});\nvar Currency = /*#__PURE__*/ function(Currency) {\n    Currency[\"USD\"] = \"USD\";\n    Currency[\"EGP\"] = \"EGP\";\n    Currency[\"EUR\"] = \"EUR\";\n    return Currency;\n}({});\nvar Gender = /*#__PURE__*/ function(Gender) {\n    Gender[\"MALE\"] = \"male\";\n    Gender[\"FEMALE\"] = \"female\";\n    Gender[\"OTHER\"] = \"other\";\n    return Gender;\n}({});\nvar AppointmentType = /*#__PURE__*/ function(AppointmentType) {\n    AppointmentType[\"CONSULTATION\"] = \"consultation\";\n    AppointmentType[\"CLEANING\"] = \"cleaning\";\n    AppointmentType[\"FILLING\"] = \"filling\";\n    AppointmentType[\"ROOT_CANAL\"] = \"root_canal\";\n    AppointmentType[\"EXTRACTION\"] = \"extraction\";\n    AppointmentType[\"CHECKUP\"] = \"checkup\";\n    AppointmentType[\"EMERGENCY\"] = \"emergency\";\n    AppointmentType[\"FOLLOW_UP\"] = \"follow_up\";\n    return AppointmentType;\n}({});\nvar AppointmentStatus = /*#__PURE__*/ function(AppointmentStatus) {\n    AppointmentStatus[\"SCHEDULED\"] = \"scheduled\";\n    AppointmentStatus[\"CONFIRMED\"] = \"confirmed\";\n    AppointmentStatus[\"IN_PROGRESS\"] = \"in_progress\";\n    AppointmentStatus[\"COMPLETED\"] = \"completed\";\n    AppointmentStatus[\"CANCELLED\"] = \"cancelled\";\n    AppointmentStatus[\"NO_SHOW\"] = \"no_show\";\n    return AppointmentStatus;\n}({});\nvar AppointmentSource = /*#__PURE__*/ function(AppointmentSource) {\n    AppointmentSource[\"DOCTOR\"] = \"doctor\";\n    AppointmentSource[\"PATIENT_ONLINE\"] = \"patient_online\";\n    AppointmentSource[\"PHONE\"] = \"phone\";\n    AppointmentSource[\"WALK_IN\"] = \"walk_in\";\n    return AppointmentSource;\n}({});\nvar MaterialCategory = /*#__PURE__*/ function(MaterialCategory) {\n    MaterialCategory[\"FILLING\"] = \"filling\";\n    MaterialCategory[\"ANESTHETIC\"] = \"anesthetic\";\n    MaterialCategory[\"CLEANING\"] = \"cleaning\";\n    MaterialCategory[\"SURGICAL\"] = \"surgical\";\n    MaterialCategory[\"IMPRESSION\"] = \"impression\";\n    MaterialCategory[\"PROTECTIVE\"] = \"protective\";\n    MaterialCategory[\"OTHER\"] = \"other\";\n    return MaterialCategory;\n}({});\nvar MaterialLogType = /*#__PURE__*/ function(MaterialLogType) {\n    MaterialLogType[\"STOCK_IN\"] = \"stock_in\";\n    MaterialLogType[\"STOCK_OUT\"] = \"stock_out\";\n    MaterialLogType[\"ADJUSTMENT\"] = \"adjustment\";\n    MaterialLogType[\"EXPIRED\"] = \"expired\";\n    MaterialLogType[\"DAMAGED\"] = \"damaged\";\n    return MaterialLogType;\n}({});\nvar InvoiceStatus = /*#__PURE__*/ function(InvoiceStatus) {\n    InvoiceStatus[\"DRAFT\"] = \"draft\";\n    InvoiceStatus[\"SENT\"] = \"sent\";\n    InvoiceStatus[\"PAID\"] = \"paid\";\n    InvoiceStatus[\"OVERDUE\"] = \"overdue\";\n    InvoiceStatus[\"CANCELLED\"] = \"cancelled\";\n    return InvoiceStatus;\n}({});\nvar PaymentMethod = /*#__PURE__*/ function(PaymentMethod) {\n    PaymentMethod[\"CASH\"] = \"cash\";\n    PaymentMethod[\"CARD\"] = \"card\";\n    PaymentMethod[\"BANK_TRANSFER\"] = \"bank_transfer\";\n    PaymentMethod[\"INSURANCE\"] = \"insurance\";\n    PaymentMethod[\"ONLINE\"] = \"online\";\n    return PaymentMethod;\n}({});\nvar ViolationType = /*#__PURE__*/ function(ViolationType) {\n    ViolationType[\"DOMAIN_MISMATCH\"] = \"domain_mismatch\";\n    ViolationType[\"MULTIPLE_INSTANCES\"] = \"multiple_instances\";\n    ViolationType[\"UNAUTHORIZED_ACCESS\"] = \"unauthorized_access\";\n    ViolationType[\"RESALE_ATTEMPT\"] = \"resale_attempt\";\n    return ViolationType;\n}({});\nvar LogLevel = /*#__PURE__*/ function(LogLevel) {\n    LogLevel[\"INFO\"] = \"info\";\n    LogLevel[\"WARN\"] = \"warn\";\n    LogLevel[\"ERROR\"] = \"error\";\n    LogLevel[\"DEBUG\"] = \"debug\";\n    return LogLevel;\n}({});\nvar NotificationType = /*#__PURE__*/ function(NotificationType) {\n    NotificationType[\"APPOINTMENT_REMINDER\"] = \"appointment_reminder\";\n    NotificationType[\"LOW_STOCK\"] = \"low_stock\";\n    NotificationType[\"PAYMENT_DUE\"] = \"payment_due\";\n    NotificationType[\"SYSTEM_ALERT\"] = \"system_alert\";\n    NotificationType[\"NEW_PATIENT\"] = \"new_patient\";\n    NotificationType[\"SUBSCRIPTION_EXPIRY\"] = \"subscription_expiry\";\n    return NotificationType;\n}({});\nvar NotificationStatus = /*#__PURE__*/ function(NotificationStatus) {\n    NotificationStatus[\"PENDING\"] = \"pending\";\n    NotificationStatus[\"SENT\"] = \"sent\";\n    NotificationStatus[\"DELIVERED\"] = \"delivered\";\n    NotificationStatus[\"READ\"] = \"read\";\n    NotificationStatus[\"FAILED\"] = \"failed\";\n    return NotificationStatus;\n}({});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/index.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/dynamic-access-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/server/app-render/dynamic-access-async-storage.external.js" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/dynamic-access-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/@swc","vendor-chunks/isows","vendor-chunks/tailwind-merge","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CFAWZY%5CNew%20folder%20(3)%5Cdentopro-saas%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CFAWZY%5CNew%20folder%20(3)%5Cdentopro-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();