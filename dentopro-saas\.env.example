# Application Configuration
NEXT_PUBLIC_APP_NAME=DentoPro
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_VERSION=1.0.0

# Database Configuration (PostgreSQL)
DATABASE_URL=postgresql://username:password@localhost:5432/dentopro_db
DATABASE_DIRECT_URL=postgresql://username:password@localhost:5432/dentopro_db

# Authentication
NEXTAUTH_SECRET=your-nextauth-secret-here
NEXTAUTH_URL=http://localhost:3000

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# JWT Configuration
JWT_SECRET=your-jwt-secret-here
JWT_EXPIRY=24h
REFRESH_TOKEN_SECRET=your-refresh-token-secret
REFRESH_TOKEN_EXPIRY=30d

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# Stripe Product IDs
STRIPE_BASIC_PLAN_ID=price_basic_plan_id
STRIPE_PREMIUM_PLAN_ID=price_premium_plan_id

# Twilio Configuration (WhatsApp & SMS)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********
TWILIO_WHATSAPP_NUMBER=whatsapp:+**********

# Email Configuration (SendGrid/Resend)
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=DentoPro
SENDGRID_API_KEY=your-sendgrid-api-key
# OR
RESEND_API_KEY=your-resend-api-key

# File Upload Configuration
NEXT_PUBLIC_MAX_FILE_SIZE=********
NEXT_PUBLIC_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,application/pdf

# Storage Configuration (if using external storage)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=dentopro-uploads

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379

# Security Configuration
ENCRYPTION_KEY=your-32-character-encryption-key
BCRYPT_ROUNDS=12

# Rate Limiting
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000

# Monitoring and Logging
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=info

# License Configuration
LICENSE_DOMAIN_BINDING=true
LICENSE_MAX_SESSIONS=3
ANTI_RESALE_PROTECTION=true

# Feature Flags
FEATURE_WHATSAPP_INTEGRATION=true
FEATURE_INVENTORY_MANAGEMENT=true
FEATURE_ANALYTICS=true
FEATURE_MULTI_LANGUAGE=true

# Development Configuration
NODE_ENV=development
NEXT_PUBLIC_DEBUG=false

# API Configuration
API_BASE_URL=http://localhost:3000/api
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# Webhook URLs
STRIPE_WEBHOOK_URL=http://localhost:3000/api/webhooks/stripe
TWILIO_WEBHOOK_URL=http://localhost:3000/api/webhooks/twilio

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_FREQUENCY=daily
BACKUP_RETENTION_DAYS=30

# Compliance Configuration
HIPAA_COMPLIANCE=true
GDPR_COMPLIANCE=true
DATA_RETENTION_DAYS=2555

# Multi-tenant Configuration
TENANT_ISOLATION=true
SHARED_RESOURCES=false

# Performance Configuration
CACHE_TTL=3600
DATABASE_POOL_SIZE=10
MAX_CONCURRENT_REQUESTS=100

# Notification Configuration
NOTIFICATION_QUEUE_ENABLED=true
EMAIL_QUEUE_ENABLED=true
SMS_QUEUE_ENABLED=true

# Analytics Configuration
ANALYTICS_ENABLED=true
USAGE_TRACKING=true
PERFORMANCE_MONITORING=true
