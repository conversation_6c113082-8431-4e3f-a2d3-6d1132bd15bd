// Test OAuth and 2FA implementation
const fs = require('fs');
const path = require('path');

console.log('🔐 Testing OAuth & 2FA Implementation...\n');

// Test 1: Check if OAuth functions exist in useAuth hook
console.log('📋 Testing OAuth Integration:');

try {
  const authHookPath = path.join(__dirname, 'src/hooks/useAuth.tsx');
  const authContent = fs.readFileSync(authHookPath, 'utf8');
  
  const oauthFunctions = [
    'loginWithGoogle',
    'loginWithGitHub', 
    'loginWithMicrosoft'
  ];
  
  oauthFunctions.forEach(func => {
    if (authContent.includes(func)) {
      console.log(`✅ ${func} - Implemented`);
    } else {
      console.log(`❌ ${func} - Missing`);
    }
  });
  
} catch (error) {
  console.log('❌ Could not read useAuth hook');
}

// Test 2: Check if 2FA functions exist
console.log('\n📋 Testing 2FA Integration:');

try {
  const authHookPath = path.join(__dirname, 'src/hooks/useAuth.tsx');
  const authContent = fs.readFileSync(authHookPath, 'utf8');
  
  const twoFAFunctions = [
    'enable2FA',
    'verify2FA',
    'disable2FA',
    'is2FAEnabled'
  ];
  
  twoFAFunctions.forEach(func => {
    if (authContent.includes(func)) {
      console.log(`✅ ${func} - Implemented`);
    } else {
      console.log(`❌ ${func} - Missing`);
    }
  });
  
} catch (error) {
  console.log('❌ Could not read useAuth hook');
}

// Test 3: Check if 2FA API routes exist
console.log('\n📋 Testing 2FA API Routes:');

const apiRoutes = [
  'src/app/api/auth/2fa/enable/route.ts',
  'src/app/api/auth/2fa/verify/route.ts',
  'src/app/api/auth/2fa/disable/route.ts'
];

apiRoutes.forEach(route => {
  const routePath = path.join(__dirname, route);
  if (fs.existsSync(routePath)) {
    console.log(`✅ ${route} - Exists`);
  } else {
    console.log(`❌ ${route} - Missing`);
  }
});

// Test 4: Check if 2FA utility library exists
console.log('\n📋 Testing 2FA Utilities:');

const utilPath = path.join(__dirname, 'src/lib/two-factor-auth.ts');
if (fs.existsSync(utilPath)) {
  console.log('✅ Two-Factor Auth utility library - Exists');
  
  try {
    const utilContent = fs.readFileSync(utilPath, 'utf8');
    const utilFunctions = [
      'generateCode',
      'sendSMS',
      'validatePhoneNumber',
      'verifyCode'
    ];
    
    utilFunctions.forEach(func => {
      if (utilContent.includes(func)) {
        console.log(`  ✅ ${func} - Implemented`);
      } else {
        console.log(`  ❌ ${func} - Missing`);
      }
    });
  } catch (error) {
    console.log('  ❌ Could not read utility file');
  }
} else {
  console.log('❌ Two-Factor Auth utility library - Missing');
}

// Test 5: Check if OAuth callback is enhanced
console.log('\n📋 Testing OAuth Callback:');

const callbackPath = path.join(__dirname, 'src/app/auth/callback/route.ts');
if (fs.existsSync(callbackPath)) {
  console.log('✅ OAuth callback route - Exists');
  
  try {
    const callbackContent = fs.readFileSync(callbackPath, 'utf8');
    
    if (callbackContent.includes('determineUserRole')) {
      console.log('  ✅ User role determination - Implemented');
    } else {
      console.log('  ❌ User role determination - Missing');
    }
    
    if (callbackContent.includes('oauth_accounts')) {
      console.log('  ✅ OAuth account storage - Implemented');
    } else {
      console.log('  ⚠️ OAuth account storage - Not found (may need manual DB setup)');
    }
  } catch (error) {
    console.log('  ❌ Could not read callback file');
  }
} else {
  console.log('❌ OAuth callback route - Missing');
}

// Test 6: Check environment variables
console.log('\n📋 Testing Environment Configuration:');

const envPath = path.join(__dirname, '.env.local');
if (fs.existsSync(envPath)) {
  try {
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    const requiredVars = [
      'GOOGLE_CLIENT_ID',
      'GOOGLE_CLIENT_SECRET',
      'TWILIO_ACCOUNT_SID',
      'TWILIO_AUTH_TOKEN'
    ];
    
    requiredVars.forEach(varName => {
      if (envContent.includes(varName)) {
        console.log(`✅ ${varName} - Configured`);
      } else {
        console.log(`❌ ${varName} - Missing`);
      }
    });
  } catch (error) {
    console.log('❌ Could not read environment file');
  }
} else {
  console.log('❌ Environment file - Missing');
}

// Test 7: Check if 2FA database tables SQL exists
console.log('\n📋 Testing 2FA Database Setup:');

const sqlPath = path.join(__dirname, 'add-2fa-tables.sql');
if (fs.existsSync(sqlPath)) {
  console.log('✅ 2FA database setup script - Exists');
  
  try {
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');
    
    const requiredTables = [
      'two_factor_codes',
      'user_2fa_settings',
      'oauth_accounts',
      'login_attempts'
    ];
    
    requiredTables.forEach(table => {
      if (sqlContent.includes(table)) {
        console.log(`  ✅ ${table} table - Defined`);
      } else {
        console.log(`  ❌ ${table} table - Missing`);
      }
    });
  } catch (error) {
    console.log('  ❌ Could not read SQL file');
  }
} else {
  console.log('❌ 2FA database setup script - Missing');
}

console.log('\n🎯 OAuth & 2FA Implementation Test Complete!');
console.log('\n📋 Next Steps:');
console.log('1. Run the add-2fa-tables.sql script in Supabase SQL Editor');
console.log('2. Configure Google OAuth in Supabase Auth settings');
console.log('3. Add Twilio credentials to environment variables');
console.log('4. Test OAuth login and 2FA functionality');
