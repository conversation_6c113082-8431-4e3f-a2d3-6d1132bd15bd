"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"96b9d08dd7e9\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJEOlxcRkFXWllcXE5ldyBmb2xkZXIgKDMpXFxkZW50b3Byby1zYWFzXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5NmI5ZDA4ZGQ3ZTlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useAuth.tsx":
/*!*******************************!*\
  !*** ./src/hooks/useAuth.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [doctorId, setDoctorId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [patientId, setPatientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [is2FAEnabled, setIs2FAEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.createClientSupabaseClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Get initial session\n            const getInitialSession = {\n                \"AuthProvider.useEffect.getInitialSession\": async ()=>{\n                    const { data: { session }, error } = await supabase.auth.getSession();\n                    if (error) {\n                        console.error('Error getting session:', error);\n                    } else {\n                        await handleAuthStateChange(session);\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.getInitialSession\"];\n            getInitialSession();\n            // Listen for auth state changes\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    var _session_user;\n                    console.log('Auth state changed:', event, session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.email);\n                    await handleAuthStateChange(session);\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    subscription.unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        supabase.auth\n    ]);\n    const handleAuthStateChange = async (session)=>{\n        setSession(session);\n        if (session === null || session === void 0 ? void 0 : session.user) {\n            // Convert Supabase user to our User type\n            const userRole = await getUserRole(session.user);\n            const userWithRole = {\n                id: session.user.id,\n                email: session.user.email || '',\n                role: userRole,\n                createdAt: new Date(session.user.created_at),\n                updatedAt: new Date(session.user.updated_at || session.user.created_at)\n            };\n            setUser(userWithRole);\n            // Set role-specific IDs\n            if (userRole === _types__WEBPACK_IMPORTED_MODULE_2__.UserRole.DOCTOR) {\n                setDoctorId(session.user.id);\n                setPatientId(null);\n            } else if (userRole === _types__WEBPACK_IMPORTED_MODULE_2__.UserRole.PATIENT) {\n                setPatientId(session.user.id);\n                const doctorId = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.SupabaseUtils.getCurrentDoctorId();\n                setDoctorId(doctorId);\n            } else {\n                setDoctorId(null);\n                setPatientId(null);\n            }\n        } else {\n            setUser(null);\n            setDoctorId(null);\n            setPatientId(null);\n        }\n    };\n    const getUserRole = async (supabaseUser)=>{\n        var _supabaseUser_user_metadata, _supabaseUser_app_metadata;\n        // Check user metadata first\n        const metadataRole = ((_supabaseUser_user_metadata = supabaseUser.user_metadata) === null || _supabaseUser_user_metadata === void 0 ? void 0 : _supabaseUser_user_metadata.role) || ((_supabaseUser_app_metadata = supabaseUser.app_metadata) === null || _supabaseUser_app_metadata === void 0 ? void 0 : _supabaseUser_app_metadata.role);\n        if (metadataRole) {\n            return metadataRole;\n        }\n        // Check if user exists in platform_admins table\n        try {\n            const { data: admin } = await supabase.from('platform_admins').select('id').eq('id', supabaseUser.id).single();\n            if (admin) {\n                return _types__WEBPACK_IMPORTED_MODULE_2__.UserRole.SUPER_ADMIN;\n            }\n        } catch (error) {\n        // User not found in platform_admins, continue checking\n        }\n        // Check if user exists in doctors table\n        try {\n            const { data: doctor } = await supabase.from('doctors').select('id').eq('id', supabaseUser.id).single();\n            if (doctor) {\n                return _types__WEBPACK_IMPORTED_MODULE_2__.UserRole.DOCTOR;\n            }\n        } catch (error) {\n        // User not found in doctors, continue checking\n        }\n        // Check if user exists in patients table\n        try {\n            const { data: patient } = await supabase.from('patients').select('id').eq('id', supabaseUser.id).single();\n            if (patient) {\n                return _types__WEBPACK_IMPORTED_MODULE_2__.UserRole.PATIENT;\n            }\n        } catch (error) {\n        // User not found in patients\n        }\n        // Default to patient role for new users\n        return _types__WEBPACK_IMPORTED_MODULE_2__.UserRole.PATIENT;\n    };\n    const login = async (email, password)=>{\n        setLoading(true);\n        try {\n            const result = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.SupabaseUtils.signInWithPassword(email, password);\n            if (result.error) {\n                throw result.error;\n            }\n            return result;\n        } catch (error) {\n            console.error('Login failed:', error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loginWithGoogle = async ()=>{\n        setLoading(true);\n        try {\n            const { data, error } = await supabase.auth.signInWithOAuth({\n                provider: 'google',\n                options: {\n                    redirectTo: \"\".concat(window.location.origin, \"/auth/callback\"),\n                    queryParams: {\n                        access_type: 'offline',\n                        prompt: 'consent'\n                    }\n                }\n            });\n            if (error) {\n                throw error;\n            }\n            return {\n                data,\n                error: null\n            };\n        } catch (error) {\n            console.error('Google login failed:', error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loginWithGitHub = async ()=>{\n        setLoading(true);\n        try {\n            const { data, error } = await supabase.auth.signInWithOAuth({\n                provider: 'github',\n                options: {\n                    redirectTo: \"\".concat(window.location.origin, \"/auth/callback\")\n                }\n            });\n            if (error) {\n                throw error;\n            }\n            return {\n                data,\n                error: null\n            };\n        } catch (error) {\n            console.error('GitHub login failed:', error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loginWithMicrosoft = async ()=>{\n        setLoading(true);\n        try {\n            const { data, error } = await supabase.auth.signInWithOAuth({\n                provider: 'azure',\n                options: {\n                    redirectTo: \"\".concat(window.location.origin, \"/auth/callback\"),\n                    scopes: 'email profile'\n                }\n            });\n            if (error) {\n                throw error;\n            }\n            return {\n                data,\n                error: null\n            };\n        } catch (error) {\n            console.error('Microsoft login failed:', error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        setLoading(true);\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.SupabaseUtils.signOut();\n            if (error) {\n                throw error;\n            }\n        } catch (error) {\n            console.error('Logout failed:', error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const register = async (userData)=>{\n        setLoading(true);\n        try {\n            const result = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.SupabaseUtils.signUp(userData.email, userData.password, {\n                role: userData.role,\n                ...userData.metadata\n            });\n            if (result.error) {\n                throw result.error;\n            }\n            return result;\n        } catch (error) {\n            console.error('Registration failed:', error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const resetPassword = async (email)=>{\n        try {\n            const result = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.SupabaseUtils.resetPassword(email);\n            if (result.error) {\n                throw result.error;\n            }\n            return result;\n        } catch (error) {\n            console.error('Password reset failed:', error);\n            throw error;\n        }\n    };\n    const hasRole = (role)=>{\n        return (user === null || user === void 0 ? void 0 : user.role) === role;\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        login,\n        loginWithGoogle,\n        logout,\n        register,\n        resetPassword,\n        isAuthenticated: !!user,\n        hasRole,\n        isSuperAdmin: hasRole(_types__WEBPACK_IMPORTED_MODULE_2__.UserRole.SUPER_ADMIN),\n        isDoctor: hasRole(_types__WEBPACK_IMPORTED_MODULE_2__.UserRole.DOCTOR),\n        isPatient: hasRole(_types__WEBPACK_IMPORTED_MODULE_2__.UserRole.PATIENT),\n        doctorId,\n        patientId\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\FAWZY\\\\New folder (3)\\\\dentopro-saas\\\\src\\\\hooks\\\\useAuth.tsx\",\n        lineNumber: 331,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"0sRJphQc92E9vokau8mqaOYcUs4=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAuth.tsx\n"));

/***/ })

});