{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pOiyKTpSK1bOjw0PZye+iSs0YWUF5tcRbMoGCU1I/Pk=", "__NEXT_PREVIEW_MODE_ID": "1c5fb85188288d5d103ab33928e670f9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "876c99f315329ac0b3c4c0842ee28f4d1451415dc36275196202e0d23e5a4f14", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b53a73d1f07120b2112ac2f6e7e8af7f419bb6d26e893788866a3ae9358c869f"}}}, "functions": {}, "sortedMiddleware": ["/"]}