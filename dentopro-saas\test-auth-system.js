// Test authentication system functionality
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function testAuthSystem() {
  console.log('🔐 Testing Authentication System...\n');
  
  // Test 1: Supabase Connection
  console.log('📋 Test 1: Supabase Connection');
  try {
    const { data, error } = await supabase.auth.getSession();
    if (error) {
      console.log('⚠️ No active session (expected for server-side test)');
    } else {
      console.log('✅ Supabase auth connection working');
    }
  } catch (err) {
    console.log('❌ Supabase connection failed:', err.message);
  }
  
  // Test 2: Demo User Data
  console.log('\n📋 Test 2: Demo User Data');
  try {
    // Test admin user
    const { data: admin, error: adminError } = await supabase
      .from('platform_admins')
      .select('id, email, full_name, created_at')
      .eq('email', '<EMAIL>')
      .single();
    
    if (admin && !adminError) {
      console.log('✅ Demo admin user exists:', admin.email);
      console.log('  - Name:', admin.full_name);
      console.log('  - ID:', admin.id);
    } else {
      console.log('❌ Demo admin user not found');
    }
    
    // Test doctor user
    const { data: doctor, error: doctorError } = await supabase
      .from('doctors')
      .select('id, email, full_name, clinic_name, subscription_status')
      .eq('email', '<EMAIL>')
      .single();
    
    if (doctor && !doctorError) {
      console.log('✅ Demo doctor user exists:', doctor.email);
      console.log('  - Name:', doctor.full_name);
      console.log('  - Clinic:', doctor.clinic_name);
      console.log('  - Status:', doctor.subscription_status);
    } else {
      console.log('❌ Demo doctor user not found');
    }
    
  } catch (err) {
    console.log('❌ Demo user test failed:', err.message);
  }
  
  // Test 3: Password Hashing (simulate)
  console.log('\n📋 Test 3: Password Security');
  try {
    const bcrypt = require('bcrypt');
    const testPassword = 'testpassword123';
    const hashedPassword = await bcrypt.hash(testPassword, 12);
    const isValid = await bcrypt.compare(testPassword, hashedPassword);
    
    if (isValid) {
      console.log('✅ Password hashing working correctly');
      console.log('  - Original:', testPassword);
      console.log('  - Hashed:', hashedPassword.substring(0, 20) + '...');
    } else {
      console.log('❌ Password hashing failed');
    }
  } catch (err) {
    console.log('⚠️ bcrypt not installed (optional for testing)');
  }
  
  // Test 4: Role-based Access
  console.log('\n📋 Test 4: Role-based Data Access');
  try {
    // Test if RLS is working by trying to access data
    const { data: patients, error: patientError } = await supabase
      .from('patients')
      .select('id, full_name, doctor_id')
      .limit(3);
    
    if (patients && !patientError) {
      console.log(`✅ Patient data accessible (${patients.length} records)`);
      patients.forEach(patient => {
        console.log(`  - Patient: ${patient.full_name} (Doctor: ${patient.doctor_id})`);
      });
    } else {
      console.log('⚠️ Patient data access restricted (RLS working)');
    }
    
    // Test appointments
    const { data: appointments, error: appointmentError } = await supabase
      .from('appointments')
      .select('id, appointment_date, status, doctor_id')
      .limit(3);
    
    if (appointments && !appointmentError) {
      console.log(`✅ Appointment data accessible (${appointments.length} records)`);
      appointments.forEach(apt => {
        console.log(`  - Appointment: ${apt.appointment_date} (${apt.status})`);
      });
    } else {
      console.log('⚠️ Appointment data access restricted (RLS working)');
    }
    
  } catch (err) {
    console.log('❌ Role-based access test failed:', err.message);
  }
  
  // Test 5: Subscription Plans
  console.log('\n📋 Test 5: Subscription System');
  try {
    const { data: plans, error: planError } = await supabase
      .from('subscription_plans')
      .select('id, name, price, features, is_active')
      .eq('is_active', true);
    
    if (plans && !planError) {
      console.log(`✅ Subscription plans available (${plans.length} active plans)`);
      plans.forEach(plan => {
        console.log(`  - ${plan.name}: $${plan.price}/month`);
        console.log(`    Features: ${plan.features?.slice(0, 2).join(', ')}...`);
      });
    } else {
      console.log('❌ Subscription plans not found');
    }
  } catch (err) {
    console.log('❌ Subscription test failed:', err.message);
  }
  
  console.log('\n🎯 Authentication System Test Complete!');
  console.log('\n📋 Next Manual Tests:');
  console.log('1. Visit http://localhost:3000/auth/login');
  console.log('2. Try logging in with demo credentials');
  console.log('3. Test OAuth buttons (Google, GitHub, Microsoft)');
  console.log('4. Test 2FA functionality');
}

testAuthSystem();
