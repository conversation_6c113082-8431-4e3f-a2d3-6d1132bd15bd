{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pOiyKTpSK1bOjw0PZye+iSs0YWUF5tcRbMoGCU1I/Pk=", "__NEXT_PREVIEW_MODE_ID": "26ba40085a6dd189c45152f0167616c3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8d4acbb1abb52041e3c3ef69fafd193b97d6278b9b2769aee2e376502ee69176", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9a53d11089dd8efcb3c79e67a35d3ef4342e91c3f94f70007547099dd73498b8"}}}, "functions": {}, "sortedMiddleware": ["/"]}