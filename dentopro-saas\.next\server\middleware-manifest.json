{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pOiyKTpSK1bOjw0PZye+iSs0YWUF5tcRbMoGCU1I/Pk=", "__NEXT_PREVIEW_MODE_ID": "93db0871c216ed3a6169fe974484f3f9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d0901a8bed090bc680d1e2f4eb96647099f2b67910ae824eedb0342a19468d9b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c66e256716fa5796c1ea7f52b16b3f30abf4fa4ae528758f3dffa9997d3cea28"}}}, "functions": {}, "sortedMiddleware": ["/"]}