// Application Constants
export const APP_NAME = 'DentoPro';
export const APP_VERSION = '1.0.0';
export const APP_DESCRIPTION = 'Complete dental clinic management SaaS platform';

// Subscription Plans
export const SUBSCRIPTION_PLANS = {
  BASIC: {
    id: 'basic',
    name: 'Basic Plan',
    price: 9,
    currency: 'USD',
    interval: 'month',
    features: [
      'Up to 100 patients',
      'Basic appointment scheduling',
      'Treatment records',
      'Basic invoicing',
      'Email support',
      'Mobile responsive'
    ],
    limits: {
      patients: 100,
      appointments: 500,
      storage: '1GB'
    }
  },
  PREMIUM: {
    id: 'premium',
    name: 'Premium Plan',
    price: 15,
    currency: 'USD',
    interval: 'month',
    features: [
      'Unlimited patients',
      'Advanced appointment scheduling',
      'Complete treatment records',
      'Advanced invoicing & payments',
      'Inventory management',
      'WhatsApp integration',
      'Analytics & reports',
      'Priority support',
      'Mobile responsive'
    ],
    limits: {
      patients: -1, // unlimited
      appointments: -1, // unlimited
      storage: '10GB'
    }
  }
} as const;

// Trial Configuration
export const TRIAL_PERIOD_DAYS = 7;
export const TRIAL_FEATURES = SUBSCRIPTION_PLANS.PREMIUM.features;

// Currency Configuration
export const SUPPORTED_CURRENCIES = {
  USD: {
    code: 'USD',
    symbol: '$',
    name: 'US Dollar',
    locale: 'en-US'
  },
  EGP: {
    code: 'EGP',
    symbol: 'ج.م',
    name: 'Egyptian Pound',
    locale: 'ar-EG'
  },
  EUR: {
    code: 'EUR',
    symbol: '€',
    name: 'Euro',
    locale: 'en-EU'
  }
} as const;

// Language Configuration
export const SUPPORTED_LANGUAGES = {
  en: {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    direction: 'ltr'
  },
  ar: {
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    direction: 'rtl'
  },
  fr: {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    direction: 'ltr'
  }
} as const;

// Appointment Configuration
export const APPOINTMENT_TYPES = {
  CONSULTATION: {
    id: 'consultation',
    name: 'Consultation',
    duration: 30,
    color: '#3B82F6'
  },
  CLEANING: {
    id: 'cleaning',
    name: 'Dental Cleaning',
    duration: 45,
    color: '#10B981'
  },
  FILLING: {
    id: 'filling',
    name: 'Filling',
    duration: 60,
    color: '#F59E0B'
  },
  ROOT_CANAL: {
    id: 'root_canal',
    name: 'Root Canal',
    duration: 90,
    color: '#EF4444'
  },
  EXTRACTION: {
    id: 'extraction',
    name: 'Tooth Extraction',
    duration: 45,
    color: '#8B5CF6'
  },
  CHECKUP: {
    id: 'checkup',
    name: 'Regular Checkup',
    duration: 30,
    color: '#06B6D4'
  },
  EMERGENCY: {
    id: 'emergency',
    name: 'Emergency',
    duration: 60,
    color: '#DC2626'
  },
  FOLLOW_UP: {
    id: 'follow_up',
    name: 'Follow-up',
    duration: 30,
    color: '#6B7280'
  }
} as const;

// Default Working Hours
export const DEFAULT_WORKING_HOURS = {
  monday: { isOpen: true, startTime: '09:00', endTime: '17:00', breakStart: '12:00', breakEnd: '13:00' },
  tuesday: { isOpen: true, startTime: '09:00', endTime: '17:00', breakStart: '12:00', breakEnd: '13:00' },
  wednesday: { isOpen: true, startTime: '09:00', endTime: '17:00', breakStart: '12:00', breakEnd: '13:00' },
  thursday: { isOpen: true, startTime: '09:00', endTime: '17:00', breakStart: '12:00', breakEnd: '13:00' },
  friday: { isOpen: true, startTime: '09:00', endTime: '17:00', breakStart: '12:00', breakEnd: '13:00' },
  saturday: { isOpen: true, startTime: '09:00', endTime: '14:00' },
  sunday: { isOpen: false, startTime: '', endTime: '' }
} as const;

// Material Categories
export const MATERIAL_CATEGORIES = {
  FILLING: {
    id: 'filling',
    name: 'Filling Materials',
    color: '#3B82F6'
  },
  ANESTHETIC: {
    id: 'anesthetic',
    name: 'Anesthetics',
    color: '#EF4444'
  },
  CLEANING: {
    id: 'cleaning',
    name: 'Cleaning Supplies',
    color: '#10B981'
  },
  SURGICAL: {
    id: 'surgical',
    name: 'Surgical Instruments',
    color: '#8B5CF6'
  },
  IMPRESSION: {
    id: 'impression',
    name: 'Impression Materials',
    color: '#F59E0B'
  },
  PROTECTIVE: {
    id: 'protective',
    name: 'Protective Equipment',
    color: '#06B6D4'
  },
  OTHER: {
    id: 'other',
    name: 'Other',
    color: '#6B7280'
  }
} as const;

// Dental Chart Configuration
export const DENTAL_CHART = {
  ADULT_TEETH: {
    UPPER_RIGHT: [18, 17, 16, 15, 14, 13, 12, 11],
    UPPER_LEFT: [21, 22, 23, 24, 25, 26, 27, 28],
    LOWER_LEFT: [31, 32, 33, 34, 35, 36, 37, 38],
    LOWER_RIGHT: [41, 42, 43, 44, 45, 46, 47, 48]
  },
  CHILD_TEETH: {
    UPPER_RIGHT: [55, 54, 53, 52, 51],
    UPPER_LEFT: [61, 62, 63, 64, 65],
    LOWER_LEFT: [71, 72, 73, 74, 75],
    LOWER_RIGHT: [81, 82, 83, 84, 85]
  }
} as const;

// Notification Templates
export const NOTIFICATION_TEMPLATES = {
  APPOINTMENT_REMINDER_24H: {
    whatsapp: 'Hello {patientName}, this is a reminder that you have an appointment tomorrow at {time} with Dr. {doctorName} at {clinicName}. Please confirm your attendance.',
    sms: 'Reminder: Appointment tomorrow at {time} with Dr. {doctorName}. Confirm: {confirmLink}',
    email: {
      subject: 'Appointment Reminder - {clinicName}',
      body: 'Dear {patientName}, you have an appointment scheduled for tomorrow at {time}.'
    }
  },
  APPOINTMENT_REMINDER_1H: {
    whatsapp: 'Hello {patientName}, your appointment with Dr. {doctorName} is in 1 hour at {time}. See you soon!',
    sms: 'Your appointment is in 1 hour at {time} with Dr. {doctorName}.',
    email: {
      subject: 'Appointment in 1 Hour - {clinicName}',
      body: 'Your appointment is starting soon at {time}.'
    }
  },
  DOCTOR_PATIENT_ARRIVAL: {
    whatsapp: 'Dr. {doctorName}, your patient {patientName} should be arriving in 10 minutes for their {time} appointment.'
  },
  LOW_STOCK_ALERT: {
    whatsapp: 'Dr. {doctorName}, {materialName} is running low. Current stock: {currentStock} {unit}. Threshold: {threshold} {unit}.',
    email: {
      subject: 'Low Stock Alert - {materialName}',
      body: 'Your inventory item {materialName} is below the minimum threshold.'
    }
  }
} as const;

// API Configuration
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/auth/login',
    LOGOUT: '/api/auth/logout',
    REGISTER: '/api/auth/register',
    VERIFY: '/api/auth/verify',
    RESET_PASSWORD: '/api/auth/reset-password'
  },
  SUPER_ADMIN: {
    DOCTORS: '/api/admin/doctors',
    ANALYTICS: '/api/admin/analytics',
    SUBSCRIPTIONS: '/api/admin/subscriptions',
    LOGS: '/api/admin/logs',
    SETTINGS: '/api/admin/settings'
  },
  DOCTOR: {
    PROFILE: '/api/doctor/profile',
    PATIENTS: '/api/doctor/patients',
    APPOINTMENTS: '/api/doctor/appointments',
    TREATMENTS: '/api/doctor/treatments',
    INVOICES: '/api/doctor/invoices',
    INVENTORY: '/api/doctor/inventory',
    PRESCRIPTIONS: '/api/doctor/prescriptions',
    SETTINGS: '/api/doctor/settings'
  },
  PATIENT: {
    PROFILE: '/api/patient/profile',
    APPOINTMENTS: '/api/patient/appointments',
    TREATMENTS: '/api/patient/treatments',
    INVOICES: '/api/patient/invoices',
    PRESCRIPTIONS: '/api/patient/prescriptions'
  },
  PUBLIC: {
    BOOK_APPOINTMENT: '/api/public/book-appointment',
    AVAILABLE_SLOTS: '/api/public/available-slots',
    CLINIC_INFO: '/api/public/clinic-info'
  }
} as const;

// Security Configuration
export const SECURITY_CONFIG = {
  PASSWORD_MIN_LENGTH: 8,
  PASSWORD_REQUIRE_UPPERCASE: true,
  PASSWORD_REQUIRE_LOWERCASE: true,
  PASSWORD_REQUIRE_NUMBERS: true,
  PASSWORD_REQUIRE_SYMBOLS: true,
  SESSION_TIMEOUT_MINUTES: 60,
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION_MINUTES: 30,
  JWT_EXPIRY_HOURS: 24,
  REFRESH_TOKEN_EXPIRY_DAYS: 30
} as const;

// File Upload Configuration
export const FILE_UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  ALLOWED_DOCUMENT_TYPES: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  MAX_FILES_PER_UPLOAD: 5
} as const;

// Pagination Configuration
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  MIN_PAGE_SIZE: 5
} as const;

// License Configuration
export const LICENSE_CONFIG = {
  SOFTWARE_WATERMARK: `© ${new Date().getFullYear()} DentoPro - Licensed to Single Doctor Use Only`,
  DOMAIN_BINDING_REQUIRED: true,
  MAX_CONCURRENT_SESSIONS: 3,
  USAGE_TRACKING_ENABLED: true,
  ANTI_RESALE_PROTECTION: true
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  UNAUTHORIZED: 'You are not authorized to access this resource',
  FORBIDDEN: 'Access denied',
  NOT_FOUND: 'Resource not found',
  VALIDATION_ERROR: 'Validation failed',
  SERVER_ERROR: 'Internal server error',
  NETWORK_ERROR: 'Network connection error',
  SUBSCRIPTION_EXPIRED: 'Your subscription has expired',
  TRIAL_EXPIRED: 'Your trial period has ended',
  LICENSE_VIOLATION: 'License violation detected',
  DOMAIN_MISMATCH: 'Domain verification failed'
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Successfully logged in',
  LOGOUT_SUCCESS: 'Successfully logged out',
  REGISTRATION_SUCCESS: 'Registration completed successfully',
  UPDATE_SUCCESS: 'Updated successfully',
  DELETE_SUCCESS: 'Deleted successfully',
  SAVE_SUCCESS: 'Saved successfully',
  EMAIL_SENT: 'Email sent successfully',
  APPOINTMENT_BOOKED: 'Appointment booked successfully',
  PAYMENT_SUCCESS: 'Payment processed successfully'
} as const;
