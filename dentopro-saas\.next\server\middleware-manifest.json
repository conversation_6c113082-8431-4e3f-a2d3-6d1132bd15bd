{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pOiyKTpSK1bOjw0PZye+iSs0YWUF5tcRbMoGCU1I/Pk=", "__NEXT_PREVIEW_MODE_ID": "2c612dbbfe17d3bab672ab530480f335", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1d8472e589e4e12028e2ded4da60da3347c36464b4dd9b05c857e42d8aa1b287", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0386e5d494f164053a81c5ac91f02c82f2041f08e528464aa3ab91230b5aabe4"}}}, "functions": {}, "sortedMiddleware": ["/"]}